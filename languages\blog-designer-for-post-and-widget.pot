#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Blog Designer - Post and Widget\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-08-21 11:56+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: \n"
"Language: \n"
"Plural-Forms: nplurals=INTEGER; plural=EXPRESSION;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Loco https://localise.biz/\n"
"X-Loco-Version: 2.3.3; wp-5.4.1"

#: includes/admin/settings/solution-features/basicpro-tab.php:85
msgid "1 Widget"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:176
msgid "1 Year"
msgstr ""

#: includes/admin/bdpw-how-it-work.php:187
msgid "100% Multilanguage."
msgstr ""

#: includes/admin/bdpw-how-it-work.php:173
msgid "13 Designs for Blog Post Grid Box."
msgstr ""

#: includes/admin/bdpw-how-it-work.php:167
msgid "130+ stunning and cool layouts."
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:55
msgid "2 - (Post Grid, Post Slider)"
msgstr ""

#: includes/admin/bdpw-how-it-work.php:171
msgid "24 Designs for Blog Post Masonry Layout."
msgstr ""

#: includes/admin/bdpw-how-it-work.php:170
msgid "45 Designs for Blog Post Slider/Carousel."
msgstr ""

#: includes/admin/bdpw-how-it-work.php:175
msgid "5 types of Widgets (Grid, slider and list etc)."
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:86
msgid "5 Widgets"
msgstr ""

#: includes/admin/bdpw-how-it-work.php:169
msgid "50 Designs for Blog Post Grid."
msgstr ""

#: includes/admin/bdpw-how-it-work.php:174
msgid "8 Designs for Blog Post Grid Box Slider."
msgstr ""

#: includes/admin/bdpw-how-it-work.php:172
msgid "8 Designs for Blog Post List View."
msgstr ""

#: includes/admin/bdpw-how-it-work.php:168
msgid "8 Shortcodes."
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:56
msgid ""
"9 - (Post Grid, Post List, Post Slider, Post Carousel, Recent Post, Post "
"Ticker, Post GridBox, Post GridBox Slider, Post Masonry)"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:59
msgid "Add extra power to the shortcode"
msgstr ""

#: includes/widget/latest-post-widget.php:114
msgid "All"
msgstr ""

#: includes/admin/bdpw-how-it-work.php:81
msgid "All Shortcodes"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:89
msgid "Arrange your desired post with your desired order and display"
msgstr ""

#: includes/widget/latest-post-widget.php:130
msgid "Ascending"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:169
msgid "Automatic Update "
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:104
msgid "Bevear Builder Support"
msgstr ""

#: includes/admin/class-bdpw-admin.php:37
msgid "Blog Designer"
msgstr ""

#. Name of the plugin
msgid "Blog Designer - Post and Widget"
msgstr ""

#: includes/admin/bdpw-how-it-work.php:162
msgid "Blog Designer Premium Features"
msgstr ""

#: includes/admin/bdpw-how-it-work.php:183
msgid "Blog display with categories."
msgstr ""

#: includes/admin/bdpw-how-it-work.php:84
msgid "Blog Grid Shortcode"
msgstr ""

#: templates/grid/design-1.php:39 templates/grid/design-2.php:28
#: templates/slider/design-1.php:28 templates/slider/design-2.php:30
msgid "By"
msgstr ""

#: includes/widget/latest-post-widget.php:108
msgid "Category"
msgstr ""

#: includes/admin/bdpw-how-it-work.php:94
msgid "Check Documentation"
msgstr ""

#: includes/admin/bdpw-how-it-work.php:103
msgid "Check Free Demo"
msgstr ""

#: includes/admin/bdpw-how-it-work.php:182
msgid "Custom Read More link for Blog Post."
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:129
msgid "Custom Read More link for Post "
msgstr ""

#: includes/admin/bdpw-how-it-work.php:100
msgid "Demo"
msgstr ""

#: includes/widget/latest-post-widget.php:129
msgid "Descending"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:49
msgid "Designs "
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:49
msgid "Designs that make your website better"
msgstr ""

#: includes/widget/latest-post-widget.php:101
msgid "Display Category"
msgstr ""

#: includes/widget/latest-post-widget.php:93
msgid "Display Date"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:134
msgid "Display Desired Post "
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:134
msgid "Display only the post you want"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:139
msgid "Display only the posts with particular category"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:154
msgid "Display post according to date, title and etc"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:139
msgid "Display Post for Particular Categories "
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:74
msgid "Display post in a masonry view"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:69
msgid "Display post in a Ticker view"
msgstr ""

#. Description of the plugin
msgid ""
"Display Post on your website with 2 designs(Grid and Slider) with 1 widget. "
"Also work with Gutenberg shortcode block."
msgstr ""

#: includes/widget/latest-post-widget.php:28
msgid "Displayed Latest WP Post in list view "
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:114
msgid "Divi Page Builder Native Support"
msgstr ""

#: includes/admin/bdpw-how-it-work.php:179
msgid "Divi Page Builder Native Support."
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:149
msgid "Do not display the posts for particular categories"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:144
msgid "Do not display the posts you want"
msgstr ""

#: includes/admin/bdpw-how-it-work.php:91
msgid "Documentation"
msgstr ""

#: includes/admin/bdpw-how-it-work.php:184
msgid ""
"Drag & Drop feature to display Blog post in your desired order and other 6 "
"types of order parameter."
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:89
msgid "Drag &amp; Drop Post Order Change "
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:99
msgid "Elementor Page Builder Support"
msgstr ""

#: includes/admin/bdpw-how-it-work.php:178
msgid "Elementor, Beaver and SiteOrigin Page Builder Support."
msgstr ""

#: includes/admin/bdpw-how-it-work.php:149
#, php-format
msgid ""
"Enjoyed this plugin? You can help by rate this plugin <a href=\"%s\" "
"target=\"_blank\">5 stars!"
msgstr ""

#: includes/admin/supports/gutenberg-block.php:247
msgid "Essential Plugin Blocks"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:149
msgid "Exclude Some Categories "
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:144
msgid "Exclude Some Posts "
msgstr ""

#: includes/widget/latest-post-widget.php:153
msgid "False"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:40
msgid "Free"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:119
msgid "Fusion Page Builder (Avada) native support"
msgstr ""

#: includes/admin/bdpw-how-it-work.php:180
msgid "Fusion Page Builder (Avada) native support."
msgstr ""

#: includes/admin/bdpw-how-it-work.php:189
#, php-format
msgid "Gain access to %sBlog Designer - Post and Widget Pro%s"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:169
msgid "Get automatic  plugin updates "
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:174
msgid "Get support for plugin"
msgstr ""

#: includes/admin/bdpw-how-it-work.php:56
msgid "Getting Started"
msgstr ""

#: includes/admin/bdpw-how-it-work.php:190
msgid "Grab Blog Designer Now"
msgstr ""

#: includes/bdpw-functions.php:236
msgid "Grid Design 1"
msgstr ""

#: includes/bdpw-functions.php:237
msgid "Grid Design 2"
msgstr ""

#: includes/admin/bdpw-how-it-work.php:176
msgid "Gutenberg Block Support."
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:94
msgid "Gutenberg Block Supports "
msgstr ""

#: includes/admin/bdpw-how-it-work.php:116
msgid "Gutenberg Support"
msgstr ""

#: includes/admin/bdpw-how-it-work.php:145
msgid "Help to improve this plugin!"
msgstr ""

#: includes/admin/bdpw-how-it-work.php:124
msgid "How it Work"
msgstr ""

#: includes/admin/bdpw-how-it-work.php:37
msgid "How It Works"
msgstr ""

#: includes/admin/bdpw-how-it-work.php:48
msgid "How It Works - Display and Shortcode"
msgstr ""

#: includes/admin/bdpw-how-it-work.php:69
msgid "How Shortcode Works"
msgstr ""

#. Author URI of the plugin
msgid ""
"https://www.essentialplugin.com/wordpress-plugin/blog-designer-post-and-"
"widget/"
msgstr ""

#: includes/admin/class-bdpw-admin.php:106
msgid "ID:"
msgstr ""

#: blog-designer-post-and-widget.php:172
#, php-format
msgid ""
"It looks like you had PRO version %s of this plugin activated. To avoid "
"conflicts the extra version has been deactivated and we recommend you delete "
"it."
msgstr ""

#: includes/widget/latest-post-widget.php:29
#: includes/widget/latest-post-widget.php:33
msgid "Latest Blog Post List"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:170
#: includes/admin/settings/solution-features/basicpro-tab.php:171
msgid "Lifetime"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:175
msgid "Limited"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:74
msgid "Masonry View "
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:159
msgid "Multiple Slider Parameters "
msgstr ""

#: includes/admin/bdpw-how-it-work.php:178
#: includes/admin/bdpw-how-it-work.php:179
#: includes/admin/bdpw-how-it-work.php:180
msgid "New"
msgstr ""

#: includes/bdpw-functions.php:181 includes/shortcode/wpsp-post.php:171
msgid "Next"
msgstr ""

#: includes/widget/latest-post-widget.php:84
msgid "Number of Items"
msgstr ""

#: includes/widget/latest-post-widget.php:126
#: includes/widget/latest-post-widget.php:137
msgid "Order"
msgstr ""

#: includes/admin/class-bdpw-admin.php:40
msgid "Overview"
msgstr ""

#: includes/admin/class-bdpw-admin.php:40
msgid "Overview - blog-designer-for-post-and-widget"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:79
msgid ""
"Play with all shortcode parameters with preview panel. No documentation "
"required!!"
msgstr ""

#: includes/widget/latest-post-widget.php:142
msgid "Post Author"
msgstr ""

#: includes/widget/latest-post-widget.php:140
msgid "Post Date"
msgstr ""

#: includes/shortcode/wpsp-post.php:31 includes/shortcode/wpsp-post.php:39
msgid "Post Grid View - Shortcode"
msgstr ""

#: includes/widget/latest-post-widget.php:141
msgid "Post ID"
msgstr ""

#: includes/widget/latest-post-widget.php:144
msgid "Post Modified Date"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:154
msgid "Post Order / Order By Parameters "
msgstr ""

#: includes/shortcode/wpsp-recent-post-slider.php:31
#: includes/shortcode/wpsp-recent-post-slider.php:39
msgid "Post Slider View - Shortcode"
msgstr ""

#: includes/widget/latest-post-widget.php:143
msgid "Post Title"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:43
msgid "Premium"
msgstr ""

#: includes/bdpw-functions.php:180 includes/shortcode/wpsp-post.php:172
msgid "Previous"
msgstr ""

#: includes/widget/latest-post-widget.php:145
msgid "Random"
msgstr ""

#: templates/grid/design-1.php:55 templates/grid/design-2.php:53
#: templates/slider/design-1.php:39 templates/slider/design-2.php:47
msgid "Read More"
msgstr ""

#: includes/admin/bdpw-how-it-work.php:85
msgid "Recent Post Slider Shortcode"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:129
msgid "Redirect post to third party destination if any"
msgstr ""

#: includes/shortcode/wpsp-post.php:148
#: includes/shortcode/wpsp-recent-post-slider.php:144
msgid "Replies"
msgstr ""

#: includes/shortcode/wpsp-post.php:148
#: includes/shortcode/wpsp-recent-post-slider.php:144
msgid "Reply"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:79
msgid "Shortcode Generator "
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:59
msgid "Shortcode Parameters "
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:54
msgid "Shortcode provide output to the front-end side"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:54
msgid "Shortcodes "
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:109
msgid "SiteOrigin Page Builder Support"
msgstr ""

#: includes/bdpw-functions.php:251
msgid "Slider Design 1"
msgstr ""

#: includes/bdpw-functions.php:252
msgid "Slider Design 2"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:159
msgid "Slider parameters like autoplay, number of slide, sider dots and etc."
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:164
msgid "Slider RTL Support "
msgstr ""

#: includes/admin/bdpw-how-it-work.php:186
msgid "Slider RTL support."
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:164
msgid "Slider supports for RTL website"
msgstr ""

#: includes/admin/bdpw-how-it-work.php:73
msgid "Step-1. Create a page like Blog"
msgstr ""

#: includes/admin/bdpw-how-it-work.php:60
msgid "Step-1. Go to \"Post --> Add New\"."
msgstr ""

#: includes/admin/bdpw-how-it-work.php:128
msgid "Step-1. Go to the Gutenberg editor of your page."
msgstr ""

#: includes/admin/bdpw-how-it-work.php:61
msgid "Step-2. Add post title, description and images"
msgstr ""

#: includes/admin/bdpw-how-it-work.php:74
msgid "Step-2. Put below shortcode as per your need."
msgstr ""

#: includes/admin/bdpw-how-it-work.php:129
msgid ""
"Step-2. Search \"post grid\" and \"post slider\" keyword in the Gutenberg "
"block list."
msgstr ""

#: includes/admin/bdpw-how-it-work.php:130
msgid ""
"Step-3. Add any block of blog designer and you will find its relative "
"options on the right end side."
msgstr ""

#: includes/admin/bdpw-how-it-work.php:62
msgid "Step-3. Select Category and Tags"
msgstr ""

#: includes/widget/latest-post-widget.php:151
msgid "Sticky Posts"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:174
msgid "Support "
msgstr ""

#: includes/shortcode/wpsp-post.php:146
#: includes/shortcode/wpsp-recent-post-slider.php:142
msgid "Tags: "
msgstr ""

#: includes/admin/bdpw-how-it-work.php:177
msgid "Template overriding feature support."
msgstr ""

#: blog-designer-post-and-widget.php:171
#, php-format
msgid "Thank you for activating %s"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:69
msgid "Ticker View "
msgstr ""

#: includes/widget/latest-post-widget.php:76
msgid "Title"
msgstr ""

#: includes/widget/latest-post-widget.php:154
msgid "True"
msgstr ""

#: includes/admin/bdpw-how-it-work.php:185
msgid ""
"Two type Pagination with Next – Previous or Numeric type support with grid "
"layout."
msgstr ""

#: includes/admin/class-bdpw-admin.php:43
msgid "Upgrade To Premium "
msgstr ""

#: includes/admin/class-bdpw-admin.php:43
msgid "Upgrade To PRO - Blog Designer"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:104
msgid "Use this plugin with Bevear Builder easily"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:114
msgid "Use this plugin with Divi Builder easily"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:99
msgid "Use this plugin with Elementor easily"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:119
msgid "Use this plugin with Fusion Page Builder (Avada) easily"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:94
msgid "Use this plugin with Gutenberg easily"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:109
msgid "Use this plugin with SiteOrigin easily"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:124
msgid "Use this plugin with Visual Composer/WPBakery page builder easily"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:84
msgid "Widgets"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:84
msgid "WordPress Widgets to your sidebars."
msgstr ""

#. Author of the plugin
msgid "WP OnlineSupport, Essential Plugin"
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:64
msgid "WP Templating Features "
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:124
msgid "WPBakery Page Builder Supports "
msgstr ""

#: includes/admin/bdpw-how-it-work.php:181
msgid "WPBakery Page Builder Supports."
msgstr ""

#: includes/admin/settings/solution-features/basicpro-tab.php:64
msgid "You can modify plugin html/designs in your current theme."
msgstr ""
