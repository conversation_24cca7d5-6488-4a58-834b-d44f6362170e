!function(e){function t(o){if(n[o])return n[o].exports;var l=n[o]={i:o,l:!1,exports:{}};return e[o].call(l.exports,l,l.exports,t),l.l=!0,l.exports}var n={};t.m=e,t.c=n,t.d=function(e,n,o){t.o(e,n)||Object.defineProperty(e,n,{configurable:!1,enumerable:!0,get:o})},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="",t(t.s=0)}([function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});n(1)},function(e,t,n){"use strict";var o=n(2),l=(n.n(o),n(3)),r=(n.n(l),n(4)),a=n(5),__=wp.i18n.__,i=wp.blocks.registerBlockType;i("bdpw/wpspw-post",{title:__("Post Grid","blog-designer-for-post-and-widget"),description:__("Display post in a grid view with various layouts.","blog-designer-for-post-and-widget"),icon:"sticky",category:"wpos_guten_block",keywords:[__("wpos"),__("post grid","blog-designer-for-post-and-widget"),__("wpspw-post")],supports:{html:!1,multiple:!0},getEditWrapperProps:function(e){var t=e.align;if(["wide","full"].includes(t))return{"data-align":t,"data-block-type":"bdpw-post-grid"}},edit:r.a,save:function(){return null}}),i("bdpw/wpspw-recent-post-slider",{title:__("Post Slider","blog-designer-for-post-and-widget"),description:__("Display post in a slider view with various layouts.","blog-designer-for-post-and-widget"),icon:"sticky",category:"wpos_guten_block",keywords:[__("wpos"),__("Post Slider","blog-designer-for-post-and-widget"),__("wpspw-recent-post-slider")],supports:{html:!1,multiple:!0},getEditWrapperProps:function(e){var t=e.align;if(["wide","full"].includes(t))return{"data-align":t,"data-block-type":"bdpw-post-slider"}},edit:a.a,save:function(){return null}})},function(e,t){},function(e,t){},function(e,t,n){"use strict";function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function l(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!==typeof t&&"function"!==typeof t?e:t}function r(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var a=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}(),i=wp.element,s=i.Component,d=i.Fragment,p=wp.components,g=p.PanelBody,m=p.Disabled,w=p.TextControl,c=p.ToggleControl,u=p.RangeControl,f=p.SelectControl,__=wp.i18n.__,b=(wp.data.select,wp.blockEditor),h=b.InspectorControls,E=b.BlockControls,y=b.BlockAlignmentToolbar,_=function(e){function t(){o(this,t);var e=l(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments));return e.state={isLoading:!1},e}return r(t,e),a(t,[{key:"ObjToString",value:function(e){var t=wp.blocks.getBlockAttributes("bdpw/wpspw-post"),n="";for(var o in e)e.hasOwnProperty(o)&&"undefined"!==typeof e[o]&&e[o]!==t[o]&&""!=jQuery.trim(e[o])&&(n+=o+'="'+e[o]+'" ');return n}},{key:"GetBooleanDropdown",value:function(){return[{value:"true",label:__("True","blog-designer-for-post-and-widget")},{value:"false",label:__("False","blog-designer-for-post-and-widget")}]}},{key:"GetDesigns",value:function(){return[{value:"design-1",label:__("Design 1","blog-designer-for-post-and-widget")},{value:"design-2",label:__("Design 2","blog-designer-for-post-and-widget")}]}},{key:"GetLazyloadDropdown",value:function(){return[{value:"",label:__("Select Lazyload","blog-designer-for-post-and-widget")},{value:"ondemand",label:__("Ondemand","blog-designer-for-post-and-widget")},{value:"progressive",label:__("Progressive","blog-designer-for-post-and-widget")}]}},{key:"GetPostOrderby",value:function(){return[{value:"date",label:__("Post Date","blog-designer-for-post-and-widget")},{value:"modified",label:__("Post Modified Date","blog-designer-for-post-and-widget")},{value:"title",label:__("Post Title","blog-designer-for-post-and-widget")},{value:"name",label:__("Post Slug","blog-designer-for-post-and-widget")},{value:"ID",label:__("Post ID","blog-designer-for-post-and-widget")},{value:"rand",label:__("Random","blog-designer-for-post-and-widget")}]}},{key:"GetPostOrder",value:function(){return[{value:"desc",label:__("Descending","blog-designer-for-post-and-widget")},{value:"asc",label:__("Ascending","blog-designer-for-post-and-widget")}]}},{key:"GetLinkTargetDropdown",value:function(){return[{value:"self",label:__("Same Window","blog-designer-for-post-and-widget")},{value:"blank",label:__("New Window","blog-designer-for-post-and-widget")}]}},{key:"GetPaginationType",value:function(){return[{value:"numeric",label:__("Numeric Pagination","blog-designer-for-post-and-widget")},{value:"prev-next",label:__("Previous - Next Pagination","blog-designer-for-post-and-widget")}]}},{key:"componentDidMount",value:function(){}},{key:"componentDidUpdate",value:function(e){var t=this.props.attributes.content_words_limit;e.attributes;(parseInt(t)<=0||isNaN(parseInt(t)))&&this.props.setAttributes({content_words_limit:20})}},{key:"render",value:function(){var e=this.props,t=e.attributes,n=e.setAttributes,o=t.design,l=t.grid,r=t.show_author,a=t.show_date,i=t.show_category_name,s=t.show_tags,p=t.show_comments,b=t.show_content,_=t.show_full_content,k=t.content_words_limit,v=t.limit,B=t.orderby,P=t.order,C=t.category,D=t.pagination,N=t.pagination_type,O=t.sticky_posts,S=t.align,x=(t.className,this.state.isLoading,wp.element.createElement(h,null,wp.element.createElement(g,{title:__("General Parameters","blog-designer-for-post-and-widget")},wp.element.createElement(f,{label:__("Design","blog-designer-for-post-and-widget"),value:o,options:this.GetDesigns(),onChange:function(e){return n({design:e})}}),wp.element.createElement(m,null,wp.element.createElement(w,{label:__("Blog Heading","blog-designer-for-post-and-widget"),help:wp.element.createElement("span",{className:"wpos-hidden-opts-desc"},__("Display blog post category name. Upgarade to ","blog-designer-for-post-and-widget"),wp.element.createElement("a",{href:Bdpw_Block.pro_link,target:"_blank"},__("Premium version ","blog-designer-for-post-and-widget")),__("to get this option.","blog-designer-for-post-and-widget"))})),wp.element.createElement(u,{label:__("Number of Columns","blog-designer-for-post-and-widget"),value:l,onChange:function(e){return n({grid:e})},min:1,max:4,help:__("Select number of column per row.","blog-designer-for-post-and-widget")}),wp.element.createElement(c,{label:__("Show Author","blog-designer-for-post-and-widget"),checked:!!r,onChange:function(){return n({show_author:!r})}}),wp.element.createElement(c,{label:__("Show Date","blog-designer-for-post-and-widget"),checked:!!a,onChange:function(){return n({show_date:!a})}}),wp.element.createElement(c,{label:__("Show Category Name","blog-designer-for-post-and-widget"),checked:!!i,onChange:function(){return n({show_category_name:!i})}}),wp.element.createElement(c,{label:__("Show Tags","blog-designer-for-post-and-widget"),checked:!!s,onChange:function(){return n({show_tags:!s})}}),wp.element.createElement(c,{label:__("Show Comments","blog-designer-for-post-and-widget"),checked:!!p,onChange:function(){return n({show_comments:!p})}}),wp.element.createElement(c,{label:__("Show Content","blog-designer-for-post-and-widget"),checked:!!b,onChange:function(){return n({show_content:!b})}}),b&&wp.element.createElement(c,{label:__("Show Full Content","blog-designer-for-post-and-widget"),checked:!!_,onChange:function(){return n({show_full_content:!_})}}),b&&!_&&wp.element.createElement(u,{label:__("Content Words Limit","blog-designer-for-post-and-widget"),value:k,onChange:function(e){return n({content_words_limit:e})},min:1,max:1e3,help:__("Enter content word limit.","blog-designer-for-post-and-widget")}),wp.element.createElement(m,null,wp.element.createElement(w,{label:__("Content Tail","blog-designer-for-post-and-widget"),help:wp.element.createElement("span",{className:"wpos-hidden-opts-desc"},__("Display dots after the post content as continue reading. Upgarade to ","blog-designer-for-post-and-widget"),wp.element.createElement("a",{href:Bdpw_Block.pro_link,target:"_blank"},__("Premium version ","blog-designer-for-post-and-widget")),__("to get this option.","blog-designer-for-post-and-widget"))})),wp.element.createElement(m,null,wp.element.createElement(f,{label:__("Read More","blog-designer-for-post-and-widget"),options:this.GetBooleanDropdown(),help:wp.element.createElement("span",{className:"wpos-hidden-opts-desc"},__("Show/Hide read more links. Upgarade to ","blog-designer-for-post-and-widget"),wp.element.createElement("a",{href:Bdpw_Block.pro_link,target:"_blank"},__("Premium version ","blog-designer-for-post-and-widget")),__("to get this option.","blog-designer-for-post-and-widget"))})),wp.element.createElement(m,null,wp.element.createElement(w,{label:__("Read More Text","blog-designer-for-post-and-widget"),help:wp.element.createElement("span",{className:"wpos-hidden-opts-desc"},__("Enter read more text. Upgarade to ","blog-designer-for-post-and-widget"),wp.element.createElement("a",{href:Bdpw_Block.pro_link,target:"_blank"},__("Premium version ","blog-designer-for-post-and-widget")),__("to get this option.","blog-designer-for-post-and-widget"))})),wp.element.createElement(m,null,wp.element.createElement(f,{label:__("Link Behaviour","blog-designer-for-post-and-widget"),options:this.GetLinkTargetDropdown(),help:wp.element.createElement("span",{className:"wpos-hidden-opts-desc"},__('Open link in a same window or in a new tab. Values are \u201cself" OR \u201cblank". Upgarade to ',"blog-designer-for-post-and-widget"),wp.element.createElement("a",{href:Bdpw_Block.pro_link,target:"_blank"},__("Premium version ","blog-designer-for-post-and-widget")),__("to get this option.","blog-designer-for-post-and-widget"))})),wp.element.createElement(m,null,wp.element.createElement(w,{label:__("Media Size","blog-designer-for-post-and-widget"),help:wp.element.createElement("span",{className:"wpos-hidden-opts-desc"},__("Choose WordPress registered image size. e.g thumbnail, medium, large, full. Upgarade to ","blog-designer-for-post-and-widget"),wp.element.createElement("a",{href:Bdpw_Block.pro_link,target:"_blank"},__("Premium version ","blog-designer-for-post-and-widget")),__("to get this option.","blog-designer-for-post-and-widget"))})),wp.element.createElement(m,null,wp.element.createElement(f,{label:__("Image Fit","blog-designer-for-post-and-widget"),options:this.GetBooleanDropdown(),help:wp.element.createElement("span",{className:"wpos-hidden-opts-desc"},__("Show image in box size. Upgarade to ","blog-designer-for-post-and-widget"),wp.element.createElement("a",{href:Bdpw_Block.pro_link,target:"_blank"},__("Premium version ","blog-designer-for-post-and-widget")),__("to get this option.","blog-designer-for-post-and-widget"))})),wp.element.createElement(m,null,wp.element.createElement(u,{label:__("Image Height","blog-designer-for-post-and-widget"),min:0,help:wp.element.createElement("span",{className:"wpos-hidden-opts-desc"},__("Control height of the featured image. You can enter any numeric number. e.g 500. Leave empty for default height. Upgarade to ","blog-designer-for-post-and-widget"),wp.element.createElement("a",{href:Bdpw_Block.pro_link,target:"_blank"},__("Premium version ","blog-designer-for-post-and-widget")),__("to get this option.","blog-designer-for-post-and-widget"))}))),wp.element.createElement(g,{title:__("Query Parameters","blog-designer-for-post-and-widget"),initialOpen:!1},wp.element.createElement(m,null,wp.element.createElement(w,{label:__("Post Type","blog-designer-for-post-and-widget"),help:wp.element.createElement("span",{className:"wpos-hidden-opts-desc"},__("Enter registered post type name. You can find it on plugin setting page. Note: Be sure you have added valid post type name otherwise no result will be displayed. Upgarade to ","blog-designer-for-post-and-widget"),wp.element.createElement("a",{href:Bdpw_Block.pro_link,target:"_blank"},__("Premium version ","blog-designer-for-post-and-widget")),__("to get this option.","blog-designer-for-post-and-widget"))})),wp.element.createElement(m,null,wp.element.createElement(w,{label:__("Taxonomy","blog-designer-for-post-and-widget"),help:wp.element.createElement("span",{className:"wpos-hidden-opts-desc"},__("Enter registered taxonomy name. You can find it on plugin setting page. Note: Be sure you have added valid taxonomy name otherwise no result will be displayed. Upgarade to ","blog-designer-for-post-and-widget"),wp.element.createElement("a",{href:Bdpw_Block.pro_link,target:"_blank"},__("Premium version ","blog-designer-for-post-and-widget")),__("to get this option.","blog-designer-for-post-and-widget"))})),wp.element.createElement(u,{label:__("Limit","blog-designer-for-post-and-widget"),value:v,onChange:function(e){return n({limit:e})},min:-1,max:1e3,help:__("Enter number of blog post to be displayed. Enter -1 to display all.","blog-designer-for-post-and-widget")}),wp.element.createElement(f,{label:__("Order By","blog-designer-for-post-and-widget"),value:B,options:this.GetPostOrderby(),onChange:function(e){return n({orderby:e})}}),wp.element.createElement(f,{label:__("Order","blog-designer-for-post-and-widget"),value:P,options:this.GetPostOrder(),onChange:function(e){return n({order:e})}}),wp.element.createElement(m,null,wp.element.createElement(w,{label:__("Include Author","blog-designer-for-post-and-widget"),help:wp.element.createElement("span",{className:"wpos-hidden-opts-desc"},__("Enter author id to display posts of particular author. You can pass multiple ids with comma seperated. You can find id at users listing page. Upgarade to ","blog-designer-for-post-and-widget"),wp.element.createElement("a",{href:Bdpw_Block.pro_link,target:"_blank"},__("Premium version ","blog-designer-for-post-and-widget")),__("to get this option.","blog-designer-for-post-and-widget"))})),wp.element.createElement(m,null,wp.element.createElement(w,{label:__("Exclude Author","blog-designer-for-post-and-widget"),help:wp.element.createElement("span",{className:"wpos-hidden-opts-desc"},__("Enter author id to hide post of particular author. Works only if `Include Author` field is empty. You can pass multiple ids with comma seperated. You can find id at relevant users listing page. Upgarade to ","blog-designer-for-post-and-widget"),wp.element.createElement("a",{href:Bdpw_Block.pro_link,target:"_blank"},__("Premium version ","blog-designer-for-post-and-widget")),__("to get this option.","blog-designer-for-post-and-widget"))})),wp.element.createElement(w,{label:__("Display Specific Category","blog-designer-for-post-and-widget"),value:C,onChange:function(e){return n({category:e})},help:wp.element.createElement("span",{title:__("You can pass multiple ids with comma seperated. You can find id at relevant category listing page.","blog-designer-for-post-and-widget")},__("Enter category id to display categories wise.","blog-designer-for-post-and-widget")," [?]")}),wp.element.createElement(m,null,wp.element.createElement(f,{label:__("Disply Child Category","blog-designer-for-post-and-widget"),options:this.GetBooleanDropdown(),help:wp.element.createElement("span",{className:"wpos-hidden-opts-desc"},__("If you are using parent category then whether to display child category or not. Upgarade to ","blog-designer-for-post-and-widget"),wp.element.createElement("a",{href:Bdpw_Block.pro_link,target:"_blank"},__("Premium version ","blog-designer-for-post-and-widget")),__("to get this option.","blog-designer-for-post-and-widget"))})),wp.element.createElement(m,null,wp.element.createElement(w,{label:__("Exclude Category","blog-designer-for-post-and-widget"),help:wp.element.createElement("span",{className:"wpos-hidden-opts-desc"},__("Exclude post category. Works only if `Category` field is empty. You can pass multiple ids with comma seperated. You can find id at relevant category listing page. Upgarade to ","blog-designer-for-post-and-widget"),wp.element.createElement("a",{href:Bdpw_Block.pro_link,target:"_blank"},__("Premium version ","blog-designer-for-post-and-widget")),__("to get this option.","blog-designer-for-post-and-widget"))})),wp.element.createElement(m,null,wp.element.createElement(w,{label:__("Display Specific Posts","blog-designer-for-post-and-widget"),help:wp.element.createElement("span",{className:"wpos-hidden-opts-desc"},__("Enter id of the post which you want to display. You can pass multiple ids with comma seperated. You can find id at relevant post listing page. Upgarade to ","blog-designer-for-post-and-widget"),wp.element.createElement("a",{href:Bdpw_Block.pro_link,target:"_blank"},__("Premium version ","blog-designer-for-post-and-widget")),__("to get this option.","blog-designer-for-post-and-widget"))})),wp.element.createElement(m,null,wp.element.createElement(w,{label:__("Exclude Post","blog-designer-for-post-and-widget"),help:wp.element.createElement("span",{className:"wpos-hidden-opts-desc"},__("Enter id of the post which you do not want to display. You can pass multiple ids with comma seperated. You can find id at relevant post listing page. Upgarade to ","blog-designer-for-post-and-widget"),wp.element.createElement("a",{href:Bdpw_Block.pro_link,target:"_blank"},__("Premium version ","blog-designer-for-post-and-widget")),__("to get this option.","blog-designer-for-post-and-widget"))})),-1!=v&&wp.element.createElement(f,{label:__("Pagination","blog-designer-for-post-and-widget"),value:D,options:this.GetBooleanDropdown(),onChange:function(e){return n({pagination:e})}}),-1!=v&&"true"==D&&wp.element.createElement(f,{label:__("Pagination Type","blog-designer-for-post-and-widget"),value:N,options:this.GetPaginationType(),onChange:function(e){return n({pagination_type:e})}}),wp.element.createElement(m,null,wp.element.createElement(u,{label:__("Query Offset","blog-designer-for-post-and-widget"),min:0,help:wp.element.createElement("span",{className:"wpos-hidden-opts-desc"},__("Exclude number of posts from starting. e.g if you pass 5 then it will skip first five post. Note: This will not work with limit=-1. Upgarade to ","blog-designer-for-post-and-widget"),wp.element.createElement("a",{href:Bdpw_Block.pro_link,target:"_blank"},__("Premium version ","blog-designer-for-post-and-widget")),__("to get this option.","blog-designer-for-post-and-widget"))})),wp.element.createElement(f,{label:__("Sticky Posts","blog-designer-for-post-and-widget"),value:O,options:this.GetBooleanDropdown(),onChange:function(e){return n({sticky_posts:e})}}))));return wp.element.createElement(d,null,x,wp.element.createElement(E,null,wp.element.createElement(y,{value:S,onChange:function(e){return n({align:e})},controls:["wide","full"]})),wp.element.createElement("div",{className:this.props.className},this.renderOutput()))}},{key:"renderOutput",value:function(){var e=this.props.attributes,t=this.ObjToString(e);t=t.trim();var n=t?"[wpspw_post "+t+"]":"[wpspw_post]";return wp.element.createElement("div",{className:"wpos-guten-shrt"},wp.element.createElement("div",{className:"wpos-guten-shrt-title"},wp.element.createElement("span",null,__("Blog Designer - Post and Widget - Grid","blog-designer-for-post-and-widget"))),n,wp.element.createElement("div",{className:"wpos-guten-shrt-footer"},wp.element.createElement("span",null),__("Do you want to check demo of this plugin.","blog-designer-for-post-and-widget")," ",wp.element.createElement("a",{href:Bdpw_Block.free_demo_link,target:"_blank"},__("Click here","blog-designer-for-post-and-widget"))),wp.element.createElement("div",{className:"wpos-guten-shrt-footer"},__("Do you also want to check premium version demo of this plugin.","blog-designer-for-post-and-widget")," ",wp.element.createElement("a",{href:Bdpw_Block.pro_demo_link,target:"_blank"},__("Click here","blog-designer-for-post-and-widget"))))}}]),t}(s);t.a=_},function(e,t,n){"use strict";function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function l(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!==typeof t&&"function"!==typeof t?e:t}function r(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var a=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}(),i=wp.element,s=i.Component,d=i.Fragment,p=wp.components,g=p.PanelBody,m=p.Disabled,w=p.TextControl,c=p.ToggleControl,u=p.RangeControl,f=p.SelectControl,__=wp.i18n.__,b=(wp.data.select,wp.blockEditor),h=b.InspectorControls,E=b.BlockControls,y=b.BlockAlignmentToolbar,_=function(e){function t(){o(this,t);var e=l(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments));return e.state={isLoading:!1},e}return r(t,e),a(t,[{key:"ObjToString",value:function(e){var t=wp.blocks.getBlockAttributes("bdpw/wpspw-recent-post-slider"),n="";for(var o in e)e.hasOwnProperty(o)&&"undefined"!==typeof e[o]&&e[o]!==t[o]&&""!=jQuery.trim(e[o])&&(n+=o+'="'+e[o]+'" ');return n}},{key:"GetBooleanDropdown",value:function(){return[{value:"true",label:__("True","blog-designer-for-post-and-widget")},{value:"false",label:__("False","blog-designer-for-post-and-widget")}]}},{key:"GetDesigns",value:function(){return[{value:"design-1",label:__("Design 1","blog-designer-for-post-and-widget")},{value:"design-2",label:__("Design 2","blog-designer-for-post-and-widget")}]}},{key:"GetLazyloadDropdown",value:function(){return[{value:"",label:__("Select Lazyload","blog-designer-for-post-and-widget")},{value:"ondemand",label:__("Ondemand","blog-designer-for-post-and-widget")},{value:"progressive",label:__("Progressive","blog-designer-for-post-and-widget")}]}},{key:"GetPostOrderby",value:function(){return[{value:"date",label:__("Post Date","blog-designer-for-post-and-widget")},{value:"modified",label:__("Post Modified Date","blog-designer-for-post-and-widget")},{value:"title",label:__("Post Title","blog-designer-for-post-and-widget")},{value:"name",label:__("Post Slug","blog-designer-for-post-and-widget")},{value:"ID",label:__("Post ID","blog-designer-for-post-and-widget")},{value:"rand",label:__("Random","blog-designer-for-post-and-widget")}]}},{key:"GetPostOrder",value:function(){return[{value:"desc",label:__("Descending","blog-designer-for-post-and-widget")},{value:"asc",label:__("Ascending","blog-designer-for-post-and-widget")}]}},{key:"GetLinkTargetDropdown",value:function(){return[{value:"self",label:__("Same Window","blog-designer-for-post-and-widget")},{value:"blank",label:__("New Window","blog-designer-for-post-and-widget")}]}},{key:"componentDidMount",value:function(){}},{key:"componentDidUpdate",value:function(e){e.attributes}},{key:"render",value:function(){var e=this.props,t=e.attributes,n=e.setAttributes,o=t.design,l=t.show_author,r=t.show_date,a=t.show_category_name,i=t.show_tags,s=t.show_comments,p=t.show_content,b=t.content_words_limit,_=t.dots,k=t.arrows,v=t.slides_column,B=t.slides_scroll,P=t.autoplay,C=t.autoplay_interval,D=t.speed,N=t.limit,O=t.orderby,S=t.order,x=t.category,G=t.lazyload,T=t.sticky_posts,U=t.align,Y=(t.className,this.state.isLoading,wp.element.createElement(h,null,wp.element.createElement(g,{title:__("General Parameters","blog-designer-for-post-and-widget")},wp.element.createElement(f,{label:__("Design","blog-designer-for-post-and-widget"),value:o,options:this.GetDesigns(),onChange:function(e){return n({design:e})}}),wp.element.createElement(m,null,wp.element.createElement(w,{label:__("Blog Heading","blog-designer-for-post-and-widget"),help:wp.element.createElement("span",{className:"wpos-hidden-opts-desc"},__("Display blog post category name. Upgarade to ","blog-designer-for-post-and-widget"),wp.element.createElement("a",{href:Bdpw_Block.pro_link,target:"_blank"},__("Premium version ","blog-designer-for-post-and-widget")),__("to get this option.","blog-designer-for-post-and-widget"))})),wp.element.createElement(c,{label:__("Show Author","blog-designer-for-post-and-widget"),checked:!!l,onChange:function(){return n({show_author:!l})}}),wp.element.createElement(c,{label:__("Show Date","blog-designer-for-post-and-widget"),checked:!!r,onChange:function(){return n({show_date:!r})}}),wp.element.createElement(c,{label:__("Show Category Name","blog-designer-for-post-and-widget"),checked:!!a,onChange:function(){return n({show_category_name:!a})}}),wp.element.createElement(c,{label:__("Show Tags","blog-designer-for-post-and-widget"),checked:!!i,onChange:function(){return n({show_tags:!i})}}),wp.element.createElement(c,{label:__("Show Comments","blog-designer-for-post-and-widget"),checked:!!s,onChange:function(){return n({show_comments:!s})}}),wp.element.createElement(c,{label:__("Show Content","blog-designer-for-post-and-widget"),checked:!!p,onChange:function(){return n({show_content:!p})}}),p&&wp.element.createElement(u,{label:__("Content Words Limit","blog-designer-for-post-and-widget"),value:b,onChange:function(e){return n({content_words_limit:e})},min:1,max:1e3,help:__("Enter content word limit.","blog-designer-for-post-and-widget")}),wp.element.createElement(m,null,wp.element.createElement(w,{label:__("Content Tail","blog-designer-for-post-and-widget"),help:wp.element.createElement("span",{className:"wpos-hidden-opts-desc"},__("Display dots after the post content as continue reading. Upgarade to ","blog-designer-for-post-and-widget"),wp.element.createElement("a",{href:Bdpw_Block.pro_link,target:"_blank"},__("Premium version ","blog-designer-for-post-and-widget")),__("to get this option.","blog-designer-for-post-and-widget"))})),wp.element.createElement(m,null,wp.element.createElement(f,{label:__("Read More","blog-designer-for-post-and-widget"),options:this.GetBooleanDropdown(),help:wp.element.createElement("span",{className:"wpos-hidden-opts-desc"},__("Show/Hide read more links. Upgarade to ","blog-designer-for-post-and-widget"),wp.element.createElement("a",{href:Bdpw_Block.pro_link,target:"_blank"},__("Premium version ","blog-designer-for-post-and-widget")),__("to get this option.","blog-designer-for-post-and-widget"))})),wp.element.createElement(m,null,wp.element.createElement(w,{label:__("Read More Text","blog-designer-for-post-and-widget"),help:wp.element.createElement("span",{className:"wpos-hidden-opts-desc"},__("Enter read more text. Upgarade to ","blog-designer-for-post-and-widget"),wp.element.createElement("a",{href:Bdpw_Block.pro_link,target:"_blank"},__("Premium version ","blog-designer-for-post-and-widget")),__("to get this option.","blog-designer-for-post-and-widget"))})),wp.element.createElement(m,null,wp.element.createElement(f,{label:__("Link Behaviour","blog-designer-for-post-and-widget"),options:this.GetLinkTargetDropdown(),help:wp.element.createElement("span",{className:"wpos-hidden-opts-desc"},__('Open link in a same window or in a new tab. Values are \u201cself" OR \u201cblank". Upgarade to ',"blog-designer-for-post-and-widget"),wp.element.createElement("a",{href:Bdpw_Block.pro_link,target:"_blank"},__("Premium version ","blog-designer-for-post-and-widget")),__("to get this option.","blog-designer-for-post-and-widget"))})),wp.element.createElement(m,null,wp.element.createElement(w,{label:__("Media Size","blog-designer-for-post-and-widget"),help:wp.element.createElement("span",{className:"wpos-hidden-opts-desc"},__("Choose WordPress registered image size. e.g thumbnail, medium, large, full. Upgarade to ","blog-designer-for-post-and-widget"),wp.element.createElement("a",{href:Bdpw_Block.pro_link,target:"_blank"},__("Premium version ","blog-designer-for-post-and-widget")),__("to get this option.","blog-designer-for-post-and-widget"))})),wp.element.createElement(m,null,wp.element.createElement(f,{label:__("Image Fit","blog-designer-for-post-and-widget"),options:this.GetBooleanDropdown(),help:wp.element.createElement("span",{className:"wpos-hidden-opts-desc"},__("Show image in box size. Upgarade to ","blog-designer-for-post-and-widget"),wp.element.createElement("a",{href:Bdpw_Block.pro_link,target:"_blank"},__("Premium version ","blog-designer-for-post-and-widget")),__("to get this option.","blog-designer-for-post-and-widget"))})),wp.element.createElement(m,null,wp.element.createElement(u,{label:__("Image Height","blog-designer-for-post-and-widget"),min:0,help:wp.element.createElement("span",{className:"wpos-hidden-opts-desc"},__("Control height of the featured image. You can enter any numeric number. e.g 500. Leave empty for default height. Upgarade to ","blog-designer-for-post-and-widget"),wp.element.createElement("a",{href:Bdpw_Block.pro_link,target:"_blank"},__("Premium version ","blog-designer-for-post-and-widget")),__("to get this option.","blog-designer-for-post-and-widget"))}))),wp.element.createElement(g,{title:__("Slider Parameters","blog-designer-for-post-and-widget"),initialOpen:!1},wp.element.createElement(f,{label:__("Dots","blog-designer-for-post-and-widget"),value:_,options:this.GetBooleanDropdown(),onChange:function(e){return n({dots:e})},help:__("Show pagination dots.","blog-designer-for-post-and-widget")}),wp.element.createElement(f,{label:__("Arrows","blog-designer-for-post-and-widget"),value:k,options:this.GetBooleanDropdown(),onChange:function(e){return n({arrows:e})},help:__("Show prev - next arrows.","blog-designer-for-post-and-widget")}),wp.element.createElement(u,{label:__("Slide To Show","blog-designer-for-post-and-widget"),value:v,onChange:function(e){return n({slides_column:e})},min:1,max:100,help:__("Enter number for Slide to show at a time. Enter 0 to display default value.","blog-designer-for-post-and-widget")}),wp.element.createElement(u,{label:__("Slide To Scroll","blog-designer-for-post-and-widget"),value:B,onChange:function(e){return n({slides_scroll:e})},min:1,max:100,help:__("Enter number to scroll slider at a time. Enter 0 to display default value.","blog-designer-for-post-and-widget")}),wp.element.createElement(f,{label:__("Autoplay","blog-designer-for-post-and-widget"),value:P,options:this.GetBooleanDropdown(),onChange:function(e){return n({autoplay:e})}}),"true"==P&&wp.element.createElement(u,{label:__("Autoplay Interval","blog-designer-for-post-and-widget"),value:C,onChange:function(e){return n({autoplay_interval:e})},min:1,max:5e4,help:__("Enter autoplay interval speed. Enter 0 to display default value.","blog-designer-for-post-and-widget")}),wp.element.createElement(u,{label:__("Speed","blog-designer-for-post-and-widget"),value:D,onChange:function(e){return n({speed:e})},min:1,max:5e4,help:__("Enter slide speed. Enter 0 to display default value.","blog-designer-for-post-and-widget")}),wp.element.createElement(m,null,wp.element.createElement(f,{label:__("Loop","blog-designer-for-post-and-widget"),options:this.GetBooleanDropdown(),help:wp.element.createElement("span",{className:"wpos-hidden-opts-desc"},__("Enable infinite loop for continuous sliding. Upgarade to ","blog-designer-for-post-and-widget"),wp.element.createElement("a",{href:Bdpw_Block.pro_link,target:"_blank"},__("Premium version ","blog-designer-for-post-and-widget")),__("to get this option.","blog-designer-for-post-and-widget"))})),wp.element.createElement(m,null,wp.element.createElement(f,{label:__("Cetermode","blog-designer-for-post-and-widget"),options:this.GetBooleanDropdown(),help:wp.element.createElement("span",{className:"wpos-hidden-opts-desc"},__("Enable slider center mode effect. Upgarade to ","blog-designer-for-post-and-widget"),wp.element.createElement("a",{href:Bdpw_Block.pro_link,target:"_blank"},__("Premium version ","blog-designer-for-post-and-widget")),__("to get this option.","blog-designer-for-post-and-widget"))})),wp.element.createElement(m,null,wp.element.createElement(f,{label:__("Pause On Hover","blog-designer-for-post-and-widget"),options:this.GetBooleanDropdown(),help:wp.element.createElement("span",{className:"wpos-hidden-opts-desc"},__("Pause slider autoplay on hover. Upgarade to ","blog-designer-for-post-and-widget"),wp.element.createElement("a",{href:Bdpw_Block.pro_link,target:"_blank"},__("Premium version ","blog-designer-for-post-and-widget")),__("to get this option.","blog-designer-for-post-and-widget"))})),wp.element.createElement(m,null,wp.element.createElement(f,{label:__("Pause On Focus","blog-designer-for-post-and-widget"),options:this.GetBooleanDropdown(),help:wp.element.createElement("span",{className:"wpos-hidden-opts-desc"},__("Pause slider autoplay when slider element is focused. Upgarade to ","blog-designer-for-post-and-widget"),wp.element.createElement("a",{href:Bdpw_Block.pro_link,target:"_blank"},__("Premium version ","blog-designer-for-post-and-widget")),__("to get this option.","blog-designer-for-post-and-widget"))})),wp.element.createElement(f,{label:__("Slider Lazyload","blog-designer-for-post-and-widget"),value:G,options:this.GetLazyloadDropdown(),onChange:function(e){return n({lazyload:e})},help:__("Select option to use lazy loading in slider.","blog-designer-for-post-and-widget")})),wp.element.createElement(g,{title:__("Query Parameters","blog-designer-for-post-and-widget"),initialOpen:!1},wp.element.createElement(m,null,wp.element.createElement(w,{label:__("Post Type","blog-designer-for-post-and-widget"),help:wp.element.createElement("span",{className:"wpos-hidden-opts-desc"},__("Enter registered post type name. You can find it on plugin setting page. Note: Be sure you have added valid post type name otherwise no result will be displayed. Upgarade to ","blog-designer-for-post-and-widget"),wp.element.createElement("a",{href:Bdpw_Block.pro_link,target:"_blank"},__("Premium version ","blog-designer-for-post-and-widget")),__("to get this option.","blog-designer-for-post-and-widget"))})),wp.element.createElement(m,null,wp.element.createElement(w,{label:__("Taxonomy","blog-designer-for-post-and-widget"),help:wp.element.createElement("span",{className:"wpos-hidden-opts-desc"},__("Enter registered taxonomy name. You can find it on plugin setting page. Note: Be sure you have added valid taxonomy name otherwise no result will be displayed. Upgarade to ","blog-designer-for-post-and-widget"),wp.element.createElement("a",{href:Bdpw_Block.pro_link,target:"_blank"},__("Premium version ","blog-designer-for-post-and-widget")),__("to get this option.","blog-designer-for-post-and-widget"))})),wp.element.createElement(u,{label:__("Limit","blog-designer-for-post-and-widget"),value:N,onChange:function(e){return n({limit:e})},min:-1,max:1e3,help:__("Enter number of blog post to be displayed. Enter -1 to display all.","blog-designer-for-post-and-widget")}),wp.element.createElement(f,{label:__("Order By","blog-designer-for-post-and-widget"),value:O,options:this.GetPostOrderby(),onChange:function(e){return n({orderby:e})}}),wp.element.createElement(f,{label:__("Order","blog-designer-for-post-and-widget"),value:S,options:this.GetPostOrder(),onChange:function(e){return n({order:e})}}),wp.element.createElement(m,null,wp.element.createElement(w,{label:__("Include Author","blog-designer-for-post-and-widget"),help:wp.element.createElement("span",{className:"wpos-hidden-opts-desc"},__("Enter author id to display posts of particular author. You can pass multiple ids with comma seperated. You can find id at users listing page. Upgarade to ","blog-designer-for-post-and-widget"),wp.element.createElement("a",{href:Bdpw_Block.pro_link,target:"_blank"},__("Premium version ","blog-designer-for-post-and-widget")),__("to get this option.","blog-designer-for-post-and-widget"))})),wp.element.createElement(m,null,wp.element.createElement(w,{label:__("Exclude Author","blog-designer-for-post-and-widget"),help:wp.element.createElement("span",{className:"wpos-hidden-opts-desc"},__("Enter author id to hide post of particular author. Works only if `Include Author` field is empty. You can pass multiple ids with comma seperated. You can find id at relevant users listing page. Upgarade to ","blog-designer-for-post-and-widget"),wp.element.createElement("a",{href:Bdpw_Block.pro_link,target:"_blank"},__("Premium version ","blog-designer-for-post-and-widget")),__("to get this option.","blog-designer-for-post-and-widget"))})),wp.element.createElement(w,{label:__("Display Specific Category","blog-designer-for-post-and-widget"),value:x,onChange:function(e){return n({category:e})},help:wp.element.createElement("span",{title:__("You can pass multiple ids with comma seperated. You can find id at relevant category listing page.","blog-designer-for-post-and-widget")},__("Enter category id to display categories wise.","blog-designer-for-post-and-widget")," [?]")}),wp.element.createElement(m,null,wp.element.createElement(f,{label:__("Disply Child Category","blog-designer-for-post-and-widget"),options:this.GetBooleanDropdown(),help:wp.element.createElement("span",{className:"wpos-hidden-opts-desc"},__("If you are using parent category then whether to display child category or not. Upgarade to ","blog-designer-for-post-and-widget"),wp.element.createElement("a",{href:Bdpw_Block.pro_link,target:"_blank"},__("Premium version ","blog-designer-for-post-and-widget")),__("to get this option.","blog-designer-for-post-and-widget"))})),wp.element.createElement(m,null,wp.element.createElement(w,{label:__("Exclude Category","blog-designer-for-post-and-widget"),help:wp.element.createElement("span",{className:"wpos-hidden-opts-desc"},__("Exclude post category. Works only if `Category` field is empty. You can pass multiple ids with comma seperated. You can find id at relevant category listing page. Upgarade to ","blog-designer-for-post-and-widget"),wp.element.createElement("a",{href:Bdpw_Block.pro_link,target:"_blank"},__("Premium version ","blog-designer-for-post-and-widget")),__("to get this option.","blog-designer-for-post-and-widget"))})),wp.element.createElement(m,null,wp.element.createElement(w,{label:__("Display Specific Posts","blog-designer-for-post-and-widget"),help:wp.element.createElement("span",{className:"wpos-hidden-opts-desc"},__("Enter id of the post which you want to display. You can pass multiple ids with comma seperated. You can find id at relevant post listing page. Upgarade to ","blog-designer-for-post-and-widget"),wp.element.createElement("a",{href:Bdpw_Block.pro_link,target:"_blank"},__("Premium version ","blog-designer-for-post-and-widget")),__("to get this option.","blog-designer-for-post-and-widget"))})),wp.element.createElement(m,null,wp.element.createElement(w,{label:__("Exclude Post","blog-designer-for-post-and-widget"),help:wp.element.createElement("span",{className:"wpos-hidden-opts-desc"},__("Enter id of the post which you do not want to display. You can pass multiple ids with comma seperated. You can find id at relevant post listing page. Upgarade to ","blog-designer-for-post-and-widget"),wp.element.createElement("a",{href:Bdpw_Block.pro_link,target:"_blank"},__("Premium version ","blog-designer-for-post-and-widget")),__("to get this option.","blog-designer-for-post-and-widget"))})),wp.element.createElement(m,null,wp.element.createElement(u,{label:__("Query Offset","blog-designer-for-post-and-widget"),min:0,help:wp.element.createElement("span",{className:"wpos-hidden-opts-desc"},__("Exclude number of posts from starting. e.g if you pass 5 then it will skip first five post. Note: This will not work with limit=-1. Upgarade to ","blog-designer-for-post-and-widget"),wp.element.createElement("a",{href:Bdpw_Block.pro_link,target:"_blank"},__("Premium version ","blog-designer-for-post-and-widget")),__("to get this option.","blog-designer-for-post-and-widget"))})),wp.element.createElement(f,{label:__("Sticky Posts","blog-designer-for-post-and-widget"),value:T,options:this.GetBooleanDropdown(),onChange:function(e){return n({sticky_posts:e})}}))));return wp.element.createElement(d,null,Y,wp.element.createElement(E,null,wp.element.createElement(y,{value:U,onChange:function(e){return n({align:e})},controls:["wide","full"]})),wp.element.createElement("div",{className:this.props.className},this.renderOutput()))}},{key:"renderOutput",value:function(){var e=this.props.attributes,t=this.ObjToString(e);t=t.trim();var n=t?"[wpspw_recent_post_slider "+t+"]":"[wpspw_recent_post_slider]";return wp.element.createElement("div",{className:"wpos-guten-shrt"},wp.element.createElement("div",{className:"wpos-guten-shrt-title"},wp.element.createElement("span",null,__("Blog Designer - Post and Widget - Slider","blog-designer-for-post-and-widget"))),n,wp.element.createElement("div",{className:"wpos-guten-shrt-footer"},wp.element.createElement("span",null),__("Do you want to check demo of this plugin.","blog-designer-for-post-and-widget")," ",wp.element.createElement("a",{href:Bdpw_Block.free_demo_link,target:"_blank"},__("Click here","blog-designer-for-post-and-widget"))),wp.element.createElement("div",{className:"wpos-guten-shrt-footer"},__("Do you also want to check premium version demo of this plugin.","blog-designer-for-post-and-widget")," ",wp.element.createElement("a",{href:Bdpw_Block.pro_demo_link,target:"_blank"},__("Click here","blog-designer-for-post-and-widget"))))}}]),t}(s);t.a=_}]);