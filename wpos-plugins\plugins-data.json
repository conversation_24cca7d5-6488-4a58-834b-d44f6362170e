{"info": {"page": 1, "pages": 1, "results": 39}, "plugins": [{"name": "Accordion and Accordion Slider", "slug": "accordion-and-accordion-slider", "version": "1.0.5", "author": "<a href=\"https://www.wponlinesupport.com\">WP OnlineSupport</a>", "author_profile": "https://profiles.wordpress.org/wponlinesupport", "requires": "4.0", "tested": "5.5.1", "requires_php": false, "rating": 100, "ratings": {"5": 10, "4": 0, "3": 0, "2": 0, "1": 0}, "num_ratings": 10, "support_threads": 1, "support_threads_resolved": 1, "active_installs": 2000, "downloaded": 15979, "last_updated": "2020-08-10 9:12am GMT", "added": "2017-10-12", "homepage": "https://www.wponlinesupport.com/plugins/", "short_description": "Accordion and Accordion Slider (Horizontal and Vertical) - Responsive and Touch enabled accordion for WordPress&hellip;", "download_link": "https://downloads.wordpress.org/plugin/accordion-and-accordion-slider.zip", "tags": {"accordion": "Accordion", "accordion-image-slider": "accordion image slider", "horizontal-accordion": "horizontal accordion", "vertical-accordion": "vertical accordion"}, "donate_link": "", "icons": {"1x": "https://ps.w.org/accordion-and-accordion-slider/assets/icon-128x128.png?rev=1745171"}}, {"name": "Album and Image Gallery plus Lightbox", "slug": "album-and-image-gallery-plus-lightbox", "version": "1.3.2", "author": "<a href=\"https://www.wponlinesupport.com/\">WP OnlineSupport</a>", "author_profile": "https://profiles.wordpress.org/wponlinesupport", "requires": "4.0", "tested": "5.5.1", "requires_php": false, "rating": 98, "ratings": {"5": 18, "4": 2, "3": 0, "2": 0, "1": 0}, "num_ratings": 20, "support_threads": 1, "support_threads_resolved": 1, "active_installs": 10000, "downloaded": 82102, "last_updated": "2020-09-09 11:53am GMT", "added": "2016-08-20", "homepage": "https://www.wponlinesupport.com/plugins/", "short_description": "A quick, easy way to add and display responsive image gallery and image album in&hellip;", "download_link": "https://downloads.wordpress.org/plugin/album-and-image-gallery-plus-lightbox.zip", "tags": {"album": "album", "gallery": "gallery", "image-album": "image album", "magnific-image-slider": "magnific image slider", "magnific-popup": "Magnific Popup"}, "donate_link": "", "icons": {"1x": "https://ps.w.org/album-and-image-gallery-plus-lightbox/assets/icon-128x128.png?rev=1479228"}}, {"name": "A<PERSON> Carousel", "slug": "app-mockups-carousel", "version": "1.1.1", "author": "<a href=\"https://www.wponlinesupport.com/\">WP OnlineSupport</a>", "author_profile": "https://profiles.wordpress.org/wponlinesupport", "requires": "4.0", "tested": "5.5.1", "requires_php": false, "rating": 100, "ratings": {"5": 2, "4": 0, "3": 0, "2": 0, "1": 0}, "num_ratings": 2, "support_threads": 1, "support_threads_resolved": 0, "active_installs": 300, "downloaded": 2501, "last_updated": "2020-08-13 9:53am GMT", "added": "2017-09-15", "homepage": "https://www.wponlinesupport.com/plugins", "short_description": "Plugin create custom post type – App Mock-ups Carousel, add multiple images and settings. Show&hellip;", "download_link": "https://downloads.wordpress.org/plugin/app-mockups-carousel.zip", "tags": {"app-gallery-carousel": "app gallery Carousel", "app-gallery-slider": "app gallery slider", "app-mockups-carousel": "app mockups carousel", "device-gallery-carousel": "device gallery Carousel", "swiper": "swiper"}, "donate_link": "http://idangero.us/donate/?for=Swiper%20Donation", "icons": {"1x": "https://ps.w.org/app-mockups-carousel/assets/icon-128x128.png?rev=1730298"}}, {"name": "Audio Player with Playlist Ultimate", "slug": "audio-player-with-playlist-ultimate", "version": "1.1.6", "author": "<a href=\"https://www.wponlinesupport.com\">WP OnlineSupport</a>", "author_profile": "https://profiles.wordpress.org/wponlinesupport", "requires": "4.0", "tested": "5.5.1", "requires_php": false, "rating": 72, "ratings": {"5": 6, "4": 0, "3": 0, "2": 2, "1": 2}, "num_ratings": 10, "support_threads": 3, "support_threads_resolved": 1, "active_installs": 1000, "downloaded": 16729, "last_updated": "2020-08-13 9:54am GMT", "added": "2017-09-13", "homepage": "https://www.wponlinesupport.com/plugins", "short_description": "Audio Player with Playlist Ultimate plugin is a jQuery HTML5 Music/Audio Player with Playlist comes with huge possibilities and options.", "download_link": "https://downloads.wordpress.org/plugin/audio-player-with-playlist-ultimate.zip", "tags": {"album-art": "album art", "artist": "artist", "audio-player": "audio player", "audio-player-with-playlist": "audio player with playlist", "multiple-player": "multiple player"}, "donate_link": "", "icons": {"1x": "https://ps.w.org/audio-player-with-playlist-ultimate/assets/icon-128x128.png?rev=1729068"}}, {"name": "Blog Designer &#8211; Post and Widget", "slug": "blog-designer-for-post-and-widget", "version": "2.0", "author": "<a href=\"https://www.wponlinesupport.com/\">WP OnlineSupport</a>", "author_profile": "https://profiles.wordpress.org/wponlinesupport", "requires": "4.0", "tested": "5.5.1", "requires_php": false, "rating": 94, "ratings": {"5": 20, "4": 0, "3": 1, "2": 0, "1": 1}, "num_ratings": 22, "support_threads": 2, "support_threads_resolved": 1, "active_installs": 10000, "downloaded": 113595, "last_updated": "2020-08-13 5:13am GMT", "added": "2016-10-22", "homepage": "", "short_description": "Display Post on your website with 2 designs(<PERSON><PERSON> and <PERSON><PERSON><PERSON>) with 1 widget. Also work&hellip;", "download_link": "https://downloads.wordpress.org/plugin/blog-designer-for-post-and-widget.zip", "tags": {"post": "post", "post-design": "post design", "post-designer": "post designer", "post-designs": "post designs", "post-layout": "post layout"}, "donate_link": "", "icons": {"1x": "https://ps.w.org/blog-designer-for-post-and-widget/assets/icon-128x128.png?rev=1519671"}}, {"name": "Countdown Timer Ultimate", "slug": "countdown-timer-ultimate", "version": "1.2.5", "author": "<a href=\"https://www.wponlinesupport.com/\">WP OnlineSupport</a>", "author_profile": "https://profiles.wordpress.org/wponlinesupport", "requires": "4.0", "tested": "5.5.1", "requires_php": false, "rating": 86, "ratings": {"5": 18, "4": 0, "3": 2, "2": 1, "1": 2}, "num_ratings": 23, "support_threads": 7, "support_threads_resolved": 3, "active_installs": 20000, "downloaded": 169243, "last_updated": "2020-08-13 5:15am GMT", "added": "2016-10-01", "homepage": "https://www.wponlinesupport.com/plugins/", "short_description": "A quick, easy way to add and display responsive Countdown timer on your website. Also&hellip;", "download_link": "https://downloads.wordpress.org/plugin/countdown-timer-ultimate.zip", "tags": {"countdown": "countdown", "countdown-timer": "countdown timer", "event-countdown-timer": "event countdown timer", "timer": "timer", "timer-countdown": "timer countdown"}, "donate_link": "", "icons": {"1x": "https://ps.w.org/countdown-timer-ultimate/assets/icon-128x128.png?rev=1920759"}}, {"name": "Featured Post Creative", "slug": "featured-post-creative", "version": "1.1.5", "author": "<a href=\"https://www.wponlinesupport.com/\">WP OnlineSupport</a>", "author_profile": "https://profiles.wordpress.org/wponlinesupport", "requires": "4.0", "tested": "5.5.1", "requires_php": false, "rating": 84, "ratings": {"5": 3, "4": 0, "3": 2, "2": 0, "1": 0}, "num_ratings": 5, "support_threads": 1, "support_threads_resolved": 0, "active_installs": 2000, "downloaded": 23750, "last_updated": "2020-08-13 5:33am GMT", "added": "2016-10-23", "homepage": "https://www.wponlinesupport.com/plugins/", "short_description": "Display Featured post on your website with 2 shortcode and 1 widget. Also work with Gutenberg shortcode block.", "download_link": "https://downloads.wordpress.org/plugin/featured-post-creative.zip", "tags": {"featured-post": "featured post", "featured-post-grid": "featured post grid", "featured-post-widget": "featured post widget", "responsive-featured-post": "responsive featured post", "responsive-featured-post-grid": "responsive featured post grid"}, "donate_link": "", "icons": {"1x": "https://ps.w.org/featured-post-creative/assets/icon-128x128.png?rev=1520278"}}, {"name": "Footer Mega Grid Columns", "slug": "footer-mega-grid-columns", "version": "1.1.2", "author": "<a href=\"https://www.wponlinesupport.com\">WP OnlineSupport</a>", "author_profile": "https://profiles.wordpress.org/wponlinesupport", "requires": "4.0", "tested": "5.5.1", "requires_php": false, "rating": 92, "ratings": {"5": 9, "4": 0, "3": 0, "2": 0, "1": 1}, "num_ratings": 10, "support_threads": 1, "support_threads_resolved": 1, "active_installs": 6000, "downloaded": 39089, "last_updated": "2020-08-10 9:07am GMT", "added": "2016-12-29", "homepage": "", "short_description": "Footer Mega Grid Columns - Register a footer widget area for your theme and allow you to add and display footer widgets in grid view with multiple col &hellip;", "download_link": "https://downloads.wordpress.org/plugin/footer-mega-grid-columns.zip", "tags": {"footer": "footer", "footer-widgets": "footer widgets", "footer-widgets-in-grid": "footer widgets in grid", "simple-footer-editor": "simple footer editor", "website-footer": "website footer"}, "donate_link": "", "icons": {"1x": "https://ps.w.org/footer-mega-grid-columns/assets/icon-128x128.png?rev=1566023"}}, {"name": "Frontend Gallery Slider For ACF", "slug": "frontend-gallery-slider-for-advanced-custom-field", "version": "1.4", "author": "<a href=\"https://www.wponlinesupport.com/\">WP OnlineSupport</a>", "author_profile": "https://profiles.wordpress.org/wponlinesupport", "requires": "3.5", "tested": "5.5.1", "requires_php": false, "rating": 96, "ratings": {"5": 3, "4": 1, "3": 0, "2": 0, "1": 0}, "num_ratings": 4, "support_threads": 2, "support_threads_resolved": 1, "active_installs": 2000, "downloaded": 16797, "last_updated": "2020-08-13 5:40am GMT", "added": "2016-06-29", "homepage": "https://www.wponlinesupport.com/plugins/", "short_description": "Display Advanced Custom Field Gallery on frontend of your website with shorcode. Also work with&hellip;", "download_link": "https://downloads.wordpress.org/plugin/frontend-gallery-slider-for-advanced-custom-field.zip", "tags": {"acf-frontend-gallery-slider": "acf frontend gallery slider", "frontend-gallery-carousel": "frontend gallery Carousel", "frontend-gallery-slider": "frontend gallery slider", "slider": "slider", "wponlinesupport": "wponlinesupport"}, "donate_link": "", "icons": {"1x": "https://ps.w.org/frontend-gallery-slider-for-advanced-custom-field/assets/icon-128x128.png?rev=1445427"}}, {"name": "Hero Banner Ultimate", "slug": "hero-banner-ultimate", "version": "1.2", "author": "<a href=\"https://www.wponlinesupport.com\">WP OnlineSupport</a>", "author_profile": "https://profiles.wordpress.org/wponlinesupport", "requires": "4.0", "tested": "5.5.1", "requires_php": false, "rating": 100, "ratings": {"5": 1, "4": 0, "3": 0, "2": 0, "1": 0}, "num_ratings": 1, "support_threads": 2, "support_threads_resolved": 0, "active_installs": 1000, "downloaded": 11500, "last_updated": "2020-08-13 5:34am GMT", "added": "2017-08-09", "homepage": "https://www.wponlinesupport.com/plugins", "short_description": "Add hero banner with the help of background image OR background color OR background video.&hellip;", "download_link": "https://downloads.wordpress.org/plugin/hero-banner-ultimate.zip", "tags": {"hero-banner": "hero banner", "hero-header": "hero header", "hero-image": "hero image", "hero-video": "hero video", "video-background": "video background"}, "donate_link": "", "icons": {"1x": "https://ps.w.org/hero-banner-ultimate/assets/icon-128x128.png?rev=1710715"}}, {"name": "InboundWP &#8211; A Complete Inbound Marketing Pack", "slug": "inboundwp-lite", "version": "1.1", "author": "<a href=\"https://www.wponlinesupport.com/\">WP OnlineSupport</a>", "author_profile": "https://profiles.wordpress.org/wponlinesupport", "requires": "4.0", "tested": "5.5.1", "requires_php": "5.4", "rating": 100, "ratings": {"5": 5, "4": 0, "3": 0, "2": 0, "1": 0}, "num_ratings": 5, "support_threads": 2, "support_threads_resolved": 2, "active_installs": 300, "downloaded": 3590, "last_updated": "2020-08-13 5:41am GMT", "added": "2019-04-11", "homepage": "https://www.wponlinesupport.com/wp-plugin/inboundwp-marketing-plugin/", "short_description": "InboundWP Marketing - Spin Wheel, Deal Countdown Timer, Social Proof, Marketing PopUp, WhatsApp Chat Support,&hellip;", "download_link": "https://downloads.wordpress.org/plugin/inboundwp-lite.zip", "tags": {"better-heading": "Better Heading", "inbound": "Inbound", "inbound-marketing": "inbound marketing", "social-proof": "social proof", "spin-wheel": "Spin Wheel"}, "donate_link": "", "icons": {"1x": "https://ps.w.org/inboundwp-lite/assets/icon-128x128.png?rev=2066897"}}, {"name": "Maintenance Mode with Timer", "slug": "maintenance-mode-with-timer", "version": "1.0", "author": "<a href=\"https://www.wponlinesupport.com/\">WP OnlineSupport</a>", "author_profile": "https://profiles.wordpress.org/wponlinesupport", "requires": "3.5", "tested": "5.5.1", "requires_php": false, "rating": 0, "ratings": {"5": 0, "4": 0, "3": 0, "2": 0, "1": 0}, "num_ratings": 0, "support_threads": 1, "support_threads_resolved": 1, "active_installs": 100, "downloaded": 3480, "last_updated": "2020-08-20 6:56am GMT", "added": "2017-01-10", "homepage": "https://www.wponlinesupport.com/plugins/", "short_description": "A quick, easy way to add and display maintenance mode with countdown timer on your&hellip;", "download_link": "https://downloads.wordpress.org/plugin/maintenance-mode-with-timer.zip", "tags": {"coming-soon": "coming soon", "countdown-timer": "countdown timer", "maintenance-mode": "maintenance mode", "maintenance-mode-with-countdown-timer": "maintenance mode with countdown timer", "maintenance-mode-with-timer": "maintenance mode with timer"}, "donate_link": "", "icons": {"1x": "https://ps.w.org/maintenance-mode-with-timer/assets/icon-128x128.png?rev=1575367"}}, {"name": "Meta slider and carousel with lightbox", "slug": "meta-slider-and-carousel-with-lightbox", "version": "1.3", "author": "<a href=\"https://www.wponlinesupport.com/\">WP OnlineSupport</a>", "author_profile": "https://profiles.wordpress.org/wponlinesupport", "requires": "4.0", "tested": "5.5.1", "requires_php": false, "rating": 94, "ratings": {"5": 13, "4": 0, "3": 0, "2": 0, "1": 1}, "num_ratings": 14, "support_threads": 2, "support_threads_resolved": 0, "active_installs": 7000, "downloaded": 87067, "last_updated": "2020-09-01 9:09am GMT", "added": "2016-07-22", "homepage": "https://www.wponlinesupport.com/plugins/", "short_description": "Plugin add a gallery meta box in your post, page and create a Image gallery menu tab. Display with a lightbox. Also work with Gutenberg shortcode bloc &hellip;", "download_link": "https://downloads.wordpress.org/plugin/meta-slider-and-carousel-with-lightbox.zip", "tags": {"frontend-gallery-carousel": "frontend gallery Carousel", "frontend-gallery-slider": "frontend gallery slider", "image-carousel": "Image carousel", "image-slider": "image slider", "meta-gallery-slider": "meta gallery slider"}, "donate_link": "", "icons": {"1x": "https://ps.w.org/meta-slider-and-carousel-with-lightbox/assets/icon-128x128.jpg?rev=1458860"}}, {"name": "Popup anything on click", "slug": "popup-anything-on-click", "version": "1.7.7", "author": "<a href=\"https://www.wponlinesupport.com\">WP OnlineSupport</a>", "author_profile": "https://profiles.wordpress.org/wponlinesupport", "requires": "4.0", "tested": "5.5.1", "requires_php": false, "rating": 90, "ratings": {"5": 40, "4": 1, "3": 1, "2": 1, "1": 5}, "num_ratings": 48, "support_threads": 12, "support_threads_resolved": 5, "active_installs": 40000, "downloaded": 251622, "last_updated": "2020-08-13 4:52am GMT", "added": "2017-07-06", "homepage": "https://www.wponlinesupport.com/plugins", "short_description": "Display a modal popup by clicking on a link, image or button. Also work with Gutenberg shortcode block.", "download_link": "https://downloads.wordpress.org/plugin/popup-anything-on-click.zip", "tags": {"full-screen-popup": "full screen popup", "html-popup": "html popup", "modal": "modal", "modal-popup": "modal popup", "popup": "popup"}, "donate_link": "", "icons": {"1x": "https://ps.w.org/popup-anything-on-click/assets/icon-128x128.png?rev=1920767"}}, {"name": "Portfolio and Projects", "slug": "portfolio-and-projects", "version": "1.0.7", "author": "<a href=\"https://www.wponlinesupport.com/\">WP OnlineSupport</a>", "author_profile": "https://profiles.wordpress.org/wponlinesupport", "requires": "4.0", "tested": "5.5.1", "requires_php": false, "rating": 96, "ratings": {"5": 8, "4": 0, "3": 1, "2": 0, "1": 0}, "num_ratings": 9, "support_threads": 1, "support_threads_resolved": 1, "active_installs": 3000, "downloaded": 30760, "last_updated": "2020-08-13 9:56am GMT", "added": "2017-01-02", "homepage": "https://www.wponlinesupport.com/plugins/", "short_description": "Display Portfolio OR Projects in a grid view. Also work with Gutenberg shortcode block.", "download_link": "https://downloads.wordpress.org/plugin/portfolio-and-projects.zip", "tags": {"portfolio": "portfolio", "portfolio-listing": "portfolio listing", "project-grid": "project grid", "project-portfolio": "project portfolio", "projects": "projects"}, "donate_link": "", "icons": {"1x": "https://ps.w.org/portfolio-and-projects/assets/icon-128x128.png?rev=1566838"}}, {"name": "Post Category Image With Grid and Slider", "slug": "post-category-image-with-grid-and-slider", "version": "1.3.2", "author": "<a href=\"https://www.wponlinesupport.com\">WP OnlineSupport</a>", "author_profile": "https://profiles.wordpress.org/wponlinesupport", "requires": "3.5", "tested": "5.5.1", "requires_php": false, "rating": 100, "ratings": {"5": 4, "4": 0, "3": 0, "2": 0, "1": 0}, "num_ratings": 4, "support_threads": 2, "support_threads_resolved": 1, "active_installs": 2000, "downloaded": 16500, "last_updated": "2020-08-13 5:44am GMT", "added": "2017-08-25", "homepage": "https://www.wponlinesupport.com/plugins", "short_description": "Post Category Image With Grid and Slider plugin allow users to upload category image and&hellip;", "download_link": "https://downloads.wordpress.org/plugin/post-category-image-with-grid-and-slider.zip", "tags": {"category": "category", "category-image": "category image", "post-category-image": "post category image", "post-category-image-grid": "post category image grid", "post-category-image-slider": "post category image slider"}, "donate_link": "", "icons": {"1x": "https://ps.w.org/post-category-image-with-grid-and-slider/assets/icon-128x128.png?rev=1719140"}}, {"name": "Post grid and filter ultimate", "slug": "post-grid-and-filter-ultimate", "version": "1.2", "author": "<a href=\"https://www.wponlinesupport.com\">WP OnlineSupport</a>", "author_profile": "https://profiles.wordpress.org/wponlinesupport", "requires": "4.0", "tested": "5.5.1", "requires_php": false, "rating": 100, "ratings": {"5": 23, "4": 0, "3": 0, "2": 0, "1": 0}, "num_ratings": 23, "support_threads": 5, "support_threads_resolved": 1, "active_installs": 5000, "downloaded": 34580, "last_updated": "2020-08-27 10:44am GMT", "added": "2017-07-01", "homepage": "https://www.wponlinesupport.com/plugins/", "short_description": "A quick, easy way to display WordPress post in grid view and post grid with filter. Also work with Gutenberg shortcode block.", "download_link": "https://downloads.wordpress.org/plugin/post-grid-and-filter-ultimate.zip", "tags": {"custom-post-grid": "custom post grid", "post": "post", "post-category-filter": "post category filter", "post-filter": "post filter", "post-grid": "post grid"}, "donate_link": "", "icons": {"1x": "https://ps.w.org/post-grid-and-filter-ultimate/assets/icon-128x128.png?rev=1688607"}}, {"name": "Post Ticker Ultimate", "slug": "ticker-ultimate", "version": "1.2.6", "author": "<a href=\"https://www.wponlinesupport.com/\">WP OnlineSupport</a>", "author_profile": "https://profiles.wordpress.org/wponlinesupport", "requires": "4.0", "tested": "5.5.1", "requires_php": false, "rating": 92, "ratings": {"5": 3, "4": 2, "3": 0, "2": 0, "1": 0}, "num_ratings": 5, "support_threads": 1, "support_threads_resolved": 1, "active_installs": 2000, "downloaded": 25342, "last_updated": "2020-08-13 9:58am GMT", "added": "2016-10-10", "homepage": "", "short_description": "Add and display horizontal or vertical post ticker on your website that work with WordPress posts and Custom Post Type with the help of shortcode.", "download_link": "https://downloads.wordpress.org/plugin/ticker-ultimate.zip", "tags": {"blog-ticker": "blog ticker", "news-ticker": "news ticker", "post-ticker": "Post ticker", "ticker": "ticker", "ticker-slider": "ticker slider"}, "donate_link": "", "icons": {"1x": "https://ps.w.org/ticker-ultimate/assets/icon-128x128.png?rev=1511431"}}, {"name": "Preloader for Website", "slug": "preloader-for-website", "version": "1.0.1", "author": "<a href=\"https://www.wponlinesupport.com/\">WP OnlineSupport</a>", "author_profile": "https://profiles.wordpress.org/wponlinesupport", "requires": "3.5", "tested": "5.5.1", "requires_php": false, "rating": 100, "ratings": {"5": 1, "4": 0, "3": 0, "2": 0, "1": 0}, "num_ratings": 1, "support_threads": 0, "support_threads_resolved": 0, "active_installs": 400, "downloaded": 7953, "last_updated": "2020-08-13 10:14am GMT", "added": "2017-01-18", "homepage": "", "short_description": "Preloader for Website : A loading screen add-on for your WordPress website.", "download_link": "https://downloads.wordpress.org/plugin/preloader-for-website.zip", "tags": {"animated-pre-loader": "animated pre-loader", "animated-preloader": "animated preloader.", "loader": "loader", "page-load-animations": "page load animations", "page-loader": "page loader"}, "donate_link": "", "icons": {"1x": "https://ps.w.org/preloader-for-website/assets/icon-128x128.png?rev=1578463"}}, {"name": "Product Categories Designs for WooCommerce", "slug": "product-categories-designs-for-woocommerce", "version": "1.2.2", "author": "<a href=\"http://www.wponlinesupport.com/\">WP OnlineSupport</a>", "author_profile": "https://profiles.wordpress.org/wponlinesupport", "requires": "4.0", "tested": "5.5.1", "requires_php": false, "rating": 80, "ratings": {"5": 6, "4": 0, "3": 0, "2": 0, "1": 2}, "num_ratings": 8, "support_threads": 1, "support_threads_resolved": 1, "active_installs": 2000, "downloaded": 19735, "last_updated": "2020-08-13 5:47am GMT", "added": "2016-09-27", "homepage": "https://www.wponlinesupport.com/plugins/", "short_description": "Display WooCommerce product categories with good designs and grid and silder view. Also work with Gutenberg shortcode block.", "download_link": "https://downloads.wordpress.org/plugin/product-categories-designs-for-woocommerce.zip", "tags": {"categories-designs": "categories Designs", "categories-grid": "categories grid", "categories-slider": "categories slider", "woocommerce-categories-designs": "WooCommerce categories designs", "woocommerce-categories-slider": "WooCommerce categories slider"}, "donate_link": "", "icons": {"1x": "https://ps.w.org/product-categories-designs-for-woocommerce/assets/icon-128x128.png?rev=1503311"}}, {"name": "Product Slider and Carousel with Category for WooCommerce", "slug": "woo-product-slider-and-carousel-with-category", "version": "2.3", "author": "<a href=\"https://www.wponlinesupport.com/\">WP OnlineSupport</a>", "author_profile": "https://profiles.wordpress.org/wponlinesupport", "requires": "3.1", "tested": "5.5.1", "requires_php": false, "rating": 88, "ratings": {"5": 38, "4": 1, "3": 1, "2": 3, "1": 4}, "num_ratings": 47, "support_threads": 6, "support_threads_resolved": 3, "active_installs": 10000, "downloaded": 149596, "last_updated": "2020-08-14 8:03am GMT", "added": "2016-06-27", "homepage": "https://www.wponlinesupport.com/plugins/", "short_description": "Woocommerce Product, Best Selling Product, Featured Product Slider/Carousel with category. Also work with G<PERSON>nberg shortcode&hellip;", "download_link": "https://downloads.wordpress.org/plugin/woo-product-slider-and-carousel-with-category.zip", "tags": {"best-selling-products": "best selling products", "best-selling-products-by-category": "best selling products by category", "best-selling-products-slider": "best selling products slider", "shortcode": "shortcode", "slick-slider": "slick slider"}, "donate_link": "", "icons": {"1x": "https://ps.w.org/woo-product-slider-and-carousel-with-category/assets/icon-128x128.png?rev=2136764"}}, {"name": "Search and Navigation Popup", "slug": "search-and-navigation-popup", "version": "1.1", "author": "<a href=\"https://www.wponlinesupport.com\">WP OnlineSupport</a>", "author_profile": "https://profiles.wordpress.org/wponlinesupport", "requires": "4.0", "tested": "5.3.4", "requires_php": false, "rating": 100, "ratings": {"5": 2, "4": 0, "3": 0, "2": 0, "1": 0}, "num_ratings": 2, "support_threads": 1, "support_threads_resolved": 1, "active_installs": 1000, "downloaded": 5103, "last_updated": "2020-01-06 2:45pm GMT", "added": "2017-09-20", "homepage": "https://www.wponlinesupport.com/plugins", "short_description": "Display a search box OR navigation popup by clicking on a button.", "download_link": "https://downloads.wordpress.org/plugin/search-and-navigation-popup.zip", "tags": {"full-screen-popup": "full screen popup", "menubar-popup": "menubar popup", "navigation-popup": "navigation popup", "serchbox-popup": "serchbox popup"}, "donate_link": "", "icons": {"1x": "https://ps.w.org/search-and-navigation-popup/assets/icon-128x128.png?rev=1732636"}}, {"name": "Slider and Carousel Plus Widget for Social Media", "slug": "slider-and-carousel-plus-widget-for-instagram", "version": "1.9.3", "author": "<a href=\"https://www.wponlinesupport.com\">WP OnlineSupport</a>", "author_profile": "https://profiles.wordpress.org/wponlinesupport", "requires": "4.0", "tested": "5.5.1", "requires_php": false, "rating": 86, "ratings": {"5": 10, "4": 3, "3": 0, "2": 0, "1": 2}, "num_ratings": 15, "support_threads": 6, "support_threads_resolved": 2, "active_installs": 10000, "downloaded": 85603, "last_updated": "2020-08-13 5:32am GMT", "added": "2017-03-20", "homepage": "https://www.wponlinesupport.com/plugins/", "short_description": "A very simple plugin to display your social media pictures in", "download_link": "https://downloads.wordpress.org/plugin/slider-and-carousel-plus-widget-for-instagram.zip", "tags": {"custom-instagram-feed": "custom instagram feed", "feed": "feed", "hashtag": "hashtag", "instagram": "Instagram", "instagram-feed": "instagram feed"}, "donate_link": "", "icons": {"1x": "https://ps.w.org/slider-and-carousel-plus-widget-for-instagram/assets/icon-128x128.png?rev=2107209"}}, {"name": "SlidersPack &#8211; All In One Image/Post Slider", "slug": "sliderspack-all-in-one-image-sliders", "version": "1.21", "author": "<a href=\"https://www.wponlinesupport.com\">WP OnlineSupport</a>", "author_profile": "https://profiles.wordpress.org/wponlinesupport", "requires": "4.0", "tested": "5.5.1", "requires_php": false, "rating": 96, "ratings": {"5": 15, "4": 0, "3": 0, "2": 0, "1": 1}, "num_ratings": 16, "support_threads": 4, "support_threads_resolved": 3, "active_installs": 3000, "downloaded": 26107, "last_updated": "2020-09-21 6:22am GMT", "added": "2017-10-24", "homepage": "https://www.wponlinesupport.com/plugins/", "short_description": "SlidersPack - All In One Image Slider plus FancyBox for WordPress. Also work with WordPress&hellip;", "download_link": "https://downloads.wordpress.org/plugin/sliderspack-all-in-one-image-sliders.zip", "tags": {"bxslider": "bxslider", "fancybox": "fancybox", "flexslider": "flexslider", "logo-ticker": "logo ticker", "meta-slider": "Meta slider"}, "donate_link": "", "icons": {"1x": "https://ps.w.org/sliderspack-all-in-one-image-sliders/assets/icon-128x128.png?rev=1751649"}}, {"name": "Smooth Scroll by WPOS", "slug": "smooth-scroll-by-wpos", "version": "1.0", "author": "<a href=\"http://sptechnolab.com\">WP OnlineSupport</a>", "author_profile": "https://profiles.wordpress.org/wponlinesupport", "requires": "3.5", "tested": "5.5.1", "requires_php": false, "rating": 84, "ratings": {"5": 4, "4": 0, "3": 0, "2": 0, "1": 1}, "num_ratings": 5, "support_threads": 1, "support_threads_resolved": 0, "active_installs": 600, "downloaded": 5714, "last_updated": "2020-08-13 5:30am GMT", "added": "2017-05-05", "homepage": "", "short_description": "A simple plugin contains Smooth Scrolling To Element, Go To Top and MouseWheel Smooth Scroll. Also work with Gutenberg shortcode block.", "download_link": "https://downloads.wordpress.org/plugin/smooth-scroll-by-wpos.zip", "tags": {"go-to-top": "go-to-top", "mousewheel-scroll": "mousewheel scroll", "scroll": "scroll", "scrolling": "scrolling", "smooth-scroll": "smooth scroll"}, "donate_link": "", "icons": {"1x": "https://ps.w.org/smooth-scroll-by-wpos/assets/icon-128x128.png?rev=1651456"}}, {"name": "Styles For WP Pagenavi <PERSON>", "slug": "styles-for-wp-pagenavi-addon", "version": "1.0.3", "author": "<a href=\"https://www.wponlinesupport.com\">WP OnlineSupport</a>", "author_profile": "https://profiles.wordpress.org/wponlinesupport", "requires": "3.1", "tested": "5.4.2", "requires_php": false, "rating": 100, "ratings": {"5": 1, "4": 0, "3": 0, "2": 0, "1": 0}, "num_ratings": 1, "support_threads": 1, "support_threads_resolved": 0, "active_installs": 1000, "downloaded": 11679, "last_updated": "2020-07-14 10:29am GMT", "added": "2017-10-02", "homepage": "https://www.wponlinesupport.com/plugins", "short_description": "Adds a more styling options to Wp-PageNavi WordPress plugin OR the_posts_pagination() WordPress navigation function.", "download_link": "https://downloads.wordpress.org/plugin/styles-for-wp-pagenavi-addon.zip", "tags": {"navigation": "navigation", "pages": "pages", "pagination": "pagination", "paging": "paging"}, "donate_link": "", "icons": {"1x": "https://ps.w.org/styles-for-wp-pagenavi-addon/assets/icon-128x128.png?rev=1739334"}}, {"name": "Timeline and History slider", "slug": "timeline-and-history-slider", "version": "1.3.6", "author": "<a href=\"https://www.wponlinesupport.com/\">WP OnlineSupport</a>", "author_profile": "https://profiles.wordpress.org/wponlinesupport", "requires": "4.0", "tested": "5.5.1", "requires_php": false, "rating": 86, "ratings": {"5": 21, "4": 2, "3": 0, "2": 1, "1": 4}, "num_ratings": 28, "support_threads": 6, "support_threads_resolved": 5, "active_installs": 6000, "downloaded": 45355, "last_updated": "2020-08-13 5:23am GMT", "added": "2016-07-22", "homepage": "https://www.wponlinesupport.com/plugins/", "short_description": "Timeline Plugin for WordPress. Easy to add and display history OR timeline for your WordPress website. Also work with G<PERSON>nberg shortcode block.", "download_link": "https://downloads.wordpress.org/plugin/timeline-and-history-slider.zip", "tags": {"biography": "biography", "company-timeline": "company timeline", "history-slider": "history slider", "responsive-timeline": "Responsive Timeline", "timeline-slider": "timeline slider"}, "donate_link": "", "icons": {"1x": "https://ps.w.org/timeline-and-history-slider/assets/icon-128x128.jpg?rev=1458943"}}, {"name": "Trending/Popular Post Slider and Widget", "slug": "wp-trending-post-slider-and-widget", "version": "1.3.5", "author": "<a href=\"https://www.wponlinesupport.com/\">WP OnlineSupport</a>", "author_profile": "https://profiles.wordpress.org/wponlinesupport", "requires": "4.0", "tested": "5.5.1", "requires_php": false, "rating": 96, "ratings": {"5": 15, "4": 0, "3": 0, "2": 1, "1": 0}, "num_ratings": 16, "support_threads": 4, "support_threads_resolved": 1, "active_installs": 6000, "downloaded": 66991, "last_updated": "2020-08-13 5:28am GMT", "added": "2016-02-26", "homepage": "https://www.wponlinesupport.com/plugins/", "short_description": "A quick, easy way to add Popular/Trending posts slider, grid block and widget. Also work with G<PERSON>nberg shortcode block.", "download_link": "https://downloads.wordpress.org/plugin/wp-trending-post-slider-and-widget.zip", "tags": {"popular-post": "Popular post", "popular-posts": "popular posts", "trending-post": "trending post", "trending-posts": "trending posts", "trending-posts-carousel": "trending posts carousel"}, "donate_link": "", "icons": {"1x": "https://ps.w.org/wp-trending-post-slider-and-widget/assets/icon-128x128.png?rev=1358591"}}, {"name": "Video gallery and Player", "slug": "html5-videogallery-plus-player", "version": "2.3.5", "author": "<a href=\"https://www.wponlinesupport.com/\">WP OnlineSupport</a>", "author_profile": "https://profiles.wordpress.org/wponlinesupport", "requires": "4.0", "tested": "5.5.1", "requires_php": false, "rating": 88, "ratings": {"5": 11, "4": 1, "3": 0, "2": 0, "1": 2}, "num_ratings": 14, "support_threads": 1, "support_threads_resolved": 1, "active_installs": 3000, "downloaded": 71909, "last_updated": "2020-08-13 5:11am GMT", "added": "2013-10-26", "homepage": "https://www.wponlinesupport.com/plugins/", "short_description": "Easy to add and display your HTML5, YouTube, Vimeo vedio gallery with Magnific Popup to&hellip;", "download_link": "https://downloads.wordpress.org/plugin/html5-videogallery-plus-player.zip", "tags": {"html5-video": "HTML5 video", "video": "video", "video-gallery": "video gallery", "video-js": "video js", "youtube-video-gallery": "Youtube video gallery"}, "donate_link": "", "icons": {"1x": "https://ps.w.org/html5-videogallery-plus-player/assets/icon-128x128.png?rev=1450994"}}, {"name": "WP Blog and Widget", "slug": "wp-blog-and-widgets", "version": "1.9", "author": "<a href=\"https://www.wponlinesupport.com/\">WP OnlineSupport</a>", "author_profile": "https://profiles.wordpress.org/wponlinesupport", "requires": "4.0", "tested": "5.5.1", "requires_php": false, "rating": 90, "ratings": {"5": 19, "4": 2, "3": 1, "2": 0, "1": 2}, "num_ratings": 24, "support_threads": 3, "support_threads_resolved": 1, "active_installs": 10000, "downloaded": 231793, "last_updated": "2020-08-28 6:25am GMT", "added": "2015-09-22", "homepage": "", "short_description": "A quick, easy way to add a Blog custom post type, Blog widget to WordPress. Also, work with the Gutenberg shortcode block.", "download_link": "https://downloads.wordpress.org/plugin/wp-blog-and-widgets.zip", "tags": {"blog-design": "blog design", "blog-layout": "blog layout", "custom-blog-template": "custom blog template", "wordpress-blog": "wordpress blog", "wordpress-blog-widget": "wordpress blog widget"}, "donate_link": "", "icons": {"1x": "https://ps.w.org/wp-blog-and-widgets/assets/icon-128x128.png?rev=1402789"}}, {"name": "WP Featured Content and Slider", "slug": "wp-featured-content-and-slider", "version": "1.3.5", "author": "<a href=\"https://www.wponlinesupport.com/\">WP OnlineSupport</a>", "author_profile": "https://profiles.wordpress.org/wponlinesupport", "requires": "4.0", "tested": "5.5.1", "requires_php": false, "rating": 100, "ratings": {"5": 8, "4": 0, "3": 0, "2": 0, "1": 0}, "num_ratings": 8, "support_threads": 1, "support_threads_resolved": 0, "active_installs": 4000, "downloaded": 58126, "last_updated": "2020-08-13 5:22am GMT", "added": "2015-11-20", "homepage": "https://www.wponlinesupport.com/plugins/", "short_description": "A quick, easy way to add and display what features your company, product or service offers, using our shortcode OR template code.", "download_link": "https://downloads.wordpress.org/plugin/wp-featured-content-and-slider.zip", "tags": {"content-slider": "content slider", "featured": "featured", "features": "features", "services": "services", "slider": "slider"}, "donate_link": "", "icons": {"1x": "https://ps.w.org/wp-featured-content-and-slider/assets/icon-128x128.png?rev=1402791"}}, {"name": "WP Logo Showcase Responsive Slider", "slug": "wp-logo-showcase-responsive-slider-slider", "version": "2.6", "author": "<a href=\"https://www.wponlinesupport.com/\">WP OnlineSupport</a>", "author_profile": "https://profiles.wordpress.org/wponlinesupport", "requires": "4.0", "tested": "5.5.1", "requires_php": false, "rating": 98, "ratings": {"5": 80, "4": 0, "3": 0, "2": 0, "1": 3}, "num_ratings": 83, "support_threads": 6, "support_threads_resolved": 6, "active_installs": 60000, "downloaded": 518146, "last_updated": "2020-08-14 7:19am GMT", "added": "2015-11-04", "homepage": "https://www.wponlinesupport.com/plugins/", "short_description": "A quick, easy way to add and display Multiple reponsive logo slideshow carousel to your site quickly and easily. Also added Gutenberg block support.", "download_link": "https://downloads.wordpress.org/plugin/wp-logo-showcase-responsive-slider-slider.zip", "tags": {"client": "Client", "client-logo-carousel": "client logo carousel", "client-logo-slider": "client logo slider", "logo-slider": "logo slider", "widget": "widget"}, "donate_link": "", "icons": {"1x": "https://ps.w.org/wp-logo-showcase-responsive-slider-slider/assets/icon-128x128.png?rev=1279325"}}, {"name": "WP Modal Popup with <PERSON><PERSON>", "slug": "wp-modal-popup-with-cookie-integration", "version": "2.0", "author": "<a href=\"https://www.wponlinesupport.com/\">WP OnlineSupport</a>", "author_profile": "https://profiles.wordpress.org/wponlinesupport", "requires": "4.0", "tested": "5.5.1", "requires_php": false, "rating": 90, "ratings": {"5": 6, "4": 1, "3": 0, "2": 1, "1": 0}, "num_ratings": 8, "support_threads": 1, "support_threads_resolved": 1, "active_installs": 4000, "downloaded": 44701, "last_updated": "2020-08-13 10:11am GMT", "added": "2016-02-27", "homepage": "https://www.wponlinesupport.com/plugins/", "short_description": "WP Modal Popup with Cookie Integration is the smart, responsive, customizable and beautifully coded popup for visitors with cookie integration.", "download_link": "https://downloads.wordpress.org/plugin/wp-modal-popup-with-cookie-integration.zip", "tags": {"advertise": "advertise", "lightbox": "lightbox", "marketing": "marketing", "pop-over": "pop over", "pop-up": "pop up"}, "donate_link": "", "icons": {"1x": "https://ps.w.org/wp-modal-popup-with-cookie-integration/assets/icon-128x128.png?rev=1359371"}}, {"name": "WP News and Scrolling Widgets", "slug": "sp-news-and-widget", "version": "4.3", "author": "<a href=\"https://www.wponlinesupport.com\">WP OnlineSupport</a>", "author_profile": "https://profiles.wordpress.org/wponlinesupport", "requires": "4.0", "tested": "5.5.1", "requires_php": false, "rating": 88, "ratings": {"5": 57, "4": 1, "3": 1, "2": 0, "1": 10}, "num_ratings": 69, "support_threads": 4, "support_threads_resolved": 2, "active_installs": 20000, "downloaded": 344675, "last_updated": "2020-09-09 11:51am GMT", "added": "2013-10-16", "homepage": "", "short_description": "A quick, easy way to add a News custom post type, News widget, vertical scrolling&hellip;", "download_link": "https://downloads.wordpress.org/plugin/sp-news-and-widget.zip", "tags": {"main-news-page-scrolling": "main news page scrolling", "news-website": "news website", "wordpress-horizontal-news-plugin-widget": "wordpress horizontal news plugin widget", "wordpress-news-plugin": "wordpress news plugin", "wordpress-vertical-news-plugin-widget": "wordpress vertical news plugin widget"}, "donate_link": "", "icons": {"1x": "https://ps.w.org/sp-news-and-widget/assets/icon-128x128.png?rev=1402776"}}, {"name": "WP responsive FAQ with category plugin", "slug": "sp-faq", "version": "3.3.4", "author": "<a href=\"https://www.wponlinesupport.com\">WP OnlineSupport</a>", "author_profile": "https://profiles.wordpress.org/wponlinesupport", "requires": "4.0", "tested": "5.5.1", "requires_php": false, "rating": 86, "ratings": {"5": 12, "4": 0, "3": 1, "2": 0, "1": 2}, "num_ratings": 15, "support_threads": 1, "support_threads_resolved": 0, "active_installs": 7000, "downloaded": 80089, "last_updated": "2020-08-13 5:19am GMT", "added": "2013-10-15", "homepage": "", "short_description": "A quick, easy way to add an responsive FAQs page. You can use this plugin as a jquery ui accordion. Also work with Gutenberg shortcode block.", "download_link": "https://downloads.wordpress.org/plugin/sp-faq.zip", "tags": {"faq": "faq", "faq-list": "faq list", "faq-plugin": "faq plugin", "faqs": "faqs", "jquery-ui": "jquery ui"}, "donate_link": "", "icons": {"1x": "https://ps.w.org/sp-faq/assets/icon-128x128.png?rev=1402779"}}, {"name": "WP Responsive Recent Post Slider/Carousel", "slug": "wp-responsive-recent-post-slider", "version": "2.4", "author": "<a href=\"https://www.wponlinesupport.com/\">WP OnlineSupport</a>", "author_profile": "https://profiles.wordpress.org/wponlinesupport", "requires": "4.0", "tested": "5.5.1", "requires_php": false, "rating": 94, "ratings": {"5": 76, "4": 0, "3": 4, "2": 3, "1": 3}, "num_ratings": 86, "support_threads": 9, "support_threads_resolved": 8, "active_installs": 30000, "downloaded": 400542, "last_updated": "2020-09-22 7:22am GMT", "added": "2015-11-12", "homepage": "https://www.wponlinesupport.com/plugins/", "short_description": "Add and display Responsive WordPresss Recent Post Slider and Carousel on your website with 4&hellip;", "download_link": "https://downloads.wordpress.org/plugin/wp-responsive-recent-post-slider.zip", "tags": {"post-slider": "post slider", "posts-slider": "posts slider", "recent-post-slider": "recent post slider", "recent-posts-slider": "recent posts slider", "slider": "slider"}, "donate_link": "", "icons": {"1x": "https://ps.w.org/wp-responsive-recent-post-slider/assets/icon-128x128.png?rev=1402785"}}, {"name": "WP Slick Slider and Image Carousel", "slug": "wp-slick-slider-and-image-carousel", "version": "2.1", "author": "<a href=\"https://www.wponlinesupport.com\">WP OnlineSupport</a>", "author_profile": "https://profiles.wordpress.org/wponlinesupport", "requires": "4.0", "tested": "5.5.1", "requires_php": false, "rating": 88, "ratings": {"5": 41, "4": 3, "3": 3, "2": 0, "1": 6}, "num_ratings": 53, "support_threads": 8, "support_threads_resolved": 5, "active_installs": 20000, "downloaded": 259786, "last_updated": "2020-08-14 7:56am GMT", "added": "2015-11-17", "homepage": "https://www.wponlinesupport.com/plugins", "short_description": "A quick, easy way to add and display mulipale WP Slick Slider and carousel using a shortcode. Also added Gutenberg block support.", "download_link": "https://downloads.wordpress.org/plugin/wp-slick-slider-and-image-carousel.zip", "tags": {"image-slider": "image slider", "slick": "slick", "slick-image-slider": "slick image slider", "slick-slider": "slick slider", "wponlinesupport": "wponlinesupport"}, "donate_link": "", "icons": {"1x": "https://ps.w.org/wp-slick-slider-and-image-carousel/assets/icon-128x128.png?rev=1443298"}}, {"name": "WP Team Showcase and Slider", "slug": "wp-team-showcase-and-slider", "version": "2.1.1", "author": "<a href=\"https://www.wponlinesupport.com/\">WP OnlineSupport</a>", "author_profile": "https://profiles.wordpress.org/wponlinesupport", "requires": "4.0", "tested": "5.5.1", "requires_php": false, "rating": 92, "ratings": {"5": 15, "4": 0, "3": 1, "2": 0, "1": 1}, "num_ratings": 17, "support_threads": 1, "support_threads_resolved": 1, "active_installs": 5000, "downloaded": 65650, "last_updated": "2020-08-13 5:00am GMT", "added": "2016-01-09", "homepage": "https://www.wponlinesupport.com/plugins/", "short_description": "Easy to add and display your employees, team members in Grid view and Slider view.&hellip;", "download_link": "https://downloads.wordpress.org/plugin/wp-team-showcase-and-slider.zip", "tags": {"responsive-teamshowcase": "responsive teamshowcase", "slider": "slider", "team": "team", "team-slider": "team slider", "teamshowcase": "teamshowcase"}, "donate_link": "", "icons": {"1x": "https://ps.w.org/wp-team-showcase-and-slider/assets/icon-128x128.png?rev=2218871"}}, {"name": "WP Testimonials with rotator widget", "slug": "wp-testimonial-with-widget", "version": "2.5.1", "author": "<a href=\"https://www.wponlinesupport.com/\">WP OnlineSupport</a>", "author_profile": "https://profiles.wordpress.org/wponlinesupport", "requires": "4.0", "tested": "5.5.1", "requires_php": false, "rating": 90, "ratings": {"5": 20, "4": 0, "3": 0, "2": 0, "1": 3}, "num_ratings": 23, "support_threads": 1, "support_threads_resolved": 0, "active_installs": 10000, "downloaded": 97865, "last_updated": "2020-08-13 5:00am GMT", "added": "2015-09-07", "homepage": "https://www.wponlinesupport.com/plugins/", "short_description": "A quick, easy way to add and display responsive, clean client&#039;s testimonial on your website using a shortcode or a widget.", "download_link": "https://downloads.wordpress.org/plugin/wp-testimonial-with-widget.zip", "tags": {"testimonial": "Testimonial", "testimonials": "testimonials", "widget": "widget"}, "donate_link": "", "icons": {"1x": "https://ps.w.org/wp-testimonial-with-widget/assets/icon-128x128.png?rev=1402790"}}]}