﻿=== Blog Designer - Post and Widget ===
Contributors: essentialplugin
Tags: post design, post designer, post layout design, blog post widget, recent post slider
Requires at least: 4.0
Tested up to: 6.8.1
Stable tag: 2.7.5
License: GPLv2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html

Display Post on your website with 2 designs(Grid and Slider) with 1 widget. Also work with Gutenberg shortcode block.

== Description ==

✅ Now that you have your website ready then why don’t you **download** and try out this blog post grid/ slider to give it better functionality.

**Download now** and display multiple blog post slider using shortcode with category. Fully responsive, and  Infinite looping. Fully accessible with arrow key navigation  Autoplay, dots, arrows etc.

**Download Now** this blog post slider because It is proven that blog post sliders have been a powerful tool to present your content in a very neat manner with the help of fancy sliders and customized designs.

[FREE DEMO](https://demo.essentialplugin.com/blog-designer-post-and-widget/?utm_source=WP&utm_medium=Blog-Designer&utm_campaign=Read-Me) | [PRO DEMO](https://demo.essentialplugin.com/prodemo/blog-designer-post-and-widget/?utm_source=WP&utm_medium=Blog-Designer&utm_campaign=Read-Me)

Your customer might like the professional and fancy vibe of your site with blog post sliders

**✅ This plugin displays your WordPress posts using :**

* Blog Post Grid (2 designs)
* Blog Post Slider (2 designs)
* Blog Post Widget (1 designs)

**Download Now** it today and explore all the features.

= ✅ Features : =
[youtube https://www.youtube.com/watch?v=2bUDHQ1Y-t0]

Make your blogs looks fancy with Blog Designer Post and Widget.

Blogs are the major attraction for marketing your website. The Blog designer post and widget plugin helps your blog posts to slide and display in a fancy way that gains maximum attraction from the website visitors. It is an easy and quick process to add install and display the blog slider on your website page. 

The blog designer post and widget plugin slides and grids the blog posts in two different designs. It helps in enhancing your website page with more attraction. You can start blogging and displaying your latest blogs in a more creative way using the plugin.

**Also added Gutenberg block support.**

= ✅ Here is the plugin shortcode example =

**Blog Post Grid** 

<code>[wpspw_post]</code>

**Blog Post Slider** 

<code>[wpspw_recent_post_slider]</code>

**To display only blog 4 post:**

<code>[wpspw_post limit="4"]</code>
Where limit define the number of posts to display. You can use same parameter with slider shortcode.

**If you want to display Blog Post Slider by category then use this short code:** 

<code>[wpspw_post category="category_ID"]</code>
You can use same parameter with Slider shortcode.

**✅ We have given 2 designs. For designs use the following shortcode:**

<code>[wpspw_post design="design-1"]</code> 
Where designs are : design-1, design-2. You can use same parameter with Slider shortcode.

= ✅ Here is Template code =
<code><?php echo do_shortcode('[wpspw_post]'); ?> </code>
<code><?php echo do_shortcode('[wpspw_recent_post_slider]'); ?> </code>

= Following are Post shortcode Parameters: =
= ✅ Use Following blog post parameters with shortcode =
<code>[wpspw_post] </code>

* **Limit** : [wpspw_post limit="10"] (Display latest 10 post and then pagination).
* **Category** : [wpspw_post category="category_id"] (Display post categories wise).
* **Design** : [wpspw_post design="design-1"] (Select the grid design. Right now there are 2 designs. design-1 and design-2).
* **Grid** : [wpspw_post grid="2"] (Display post in Grid formats. You can use grid:1,2,3,4).
* **Order** : [wpspw_post order="DESC"] (Post order ie DESC or ASC).
* **Order by** : [wpspw_post orderby="date"] (Order by post ie date, ID, author, title, modified, rand and menu_order etc).
* **Sticky Posts** : [wpspw_post sticky_posts="false" ] (Show sticky posts on the top or not. By default value is "false". Options are "true" and "false").
* **Display Author** : [wpspw_post show_author="true"] (Display Post author OR not. By default value is "true". Values are "true" and "false" ).
* **Display Content** : [wpspw_post show_content="true" ] (Display post Short content OR not. By default value is "true". Options are "true" and "false").
* **Display Full Content** : [wpspw_post show_full_content="true"] (Display Full post content on main page if you do not want word limit. By default value is "false").
* **Display Date** : [wpspw_post show_date="false"] (Display Post date OR not. By default value is "true". Options are "true" and "false").
* **Display Category Name** : [wpspw_post show_category_name="true" ] (Display post category name OR not. By default value is "true". Options are "true" and "false").
* **Display comments** : [wpspw_post show_comments="true"] (Display Post comments OR not. By default value is "true". Options are "true" and "false").
* **Content Words Limit** : [wpspw_post content_words_limit="30" ] (Control post short content Words limit. By default limit is 20 words).
* **Display Tags** : [wpspw_post show_tags="true"] (show post tags or not).
* **Pagination** : [wpspw_post pagination="true"] (show post pagination or not).
* **Pagination Type** : [wpspw_post pagination_type="prev-next"] (pagination type. values are "numeric" OR "prev-next". By default is numeric).
* **extra_class** : [wpspw_post extra_class=""] (Enter extra CSS class for design customization ).

= ✅ Use Following Blog Post Slider parameters with shortcode =
<code>[wpspw_recent_post_slider]</code>

* **Limit** : [wpspw_recent_post_slider limit="10"] (Display latest 10 post in slider).
* **Design** : [wpspw_recent_post_slider design="design-1"] (Select the grid design. Right now there are 2 designs. design-1 and design-2).
* **Category** : [wpspw_recent_post_slider category="category_id"] (Display post categories wise).
* **Display Author** : [wpspw_post show_author="true"] (Display Post author OR not. By default value is "true". Values are "true" and "false" ).
* **Display Date** : [wpspw_recent_post_slider show_date="false"] (Display post date OR not. By default value is "true". Options are "true" and "false").
* **Display comments** : [wpspw_recent_post_slider show_comments="true"] (Display Post comments OR not. By default value is "true". Options are "true" and "false").
* **Display Category Name** : [wpspw_recent_post_slider show_category_name="true" ] (Display post category name OR not. By default value is "true". Options are "true" and "false").
* **Order** : [wpspw_recent_post_slider order="DESC"] (Post order i.e. DESC or ASC).
* **Order by** : [wpspw_recent_post_slider orderby="date"] (Order by post i.e. date, ID, author, title, modified, rand and menu_order etc).
* **Sticky Posts** : [wpspw_recent_post_slider sticky_posts="false" ] (Show sticky posts on the top or not. By default value is "false". Options are "true" and "false").
* **Display Content** : [wpspw_recent_post_slider show_content="true" ] (Display post Short content OR not. By default value is "true". Options are "true" and "false").
* **Content Words Limit** : [wpspw_recent_post_slider content_words_limit="30" ] (Control post short content Words limit. By default limit is 20 words).
* **Display Tags** : [wpspw_recent_post_slider show_tags="true"](show post tags or not).
* **Slides Column** : [wpspw_recent_post_slider slides_column="3"] (i.e. Display number of Post at a time).
* **Slides Scroll** : [wpspw_recent_post_slider slides_scroll="1"] (i.e. scroll number of Post at a time).
* **Pagination and Arrows** : [wpspw_recent_post_slider dots="false" arrows="false"]
* **Autoplay and Autoplay Interval :** [wpspw_recent_post_slider autoplay="true" autoplay_interval="100"]
* **Slider Speed** : [wpspw_recent_post_slider speed="3000"]
* **Lazyload** : [wpspw_recent_post_slider lazyload="ondemand"] (Lazy load images. Defaults to ''. Two options can be passed. 'ondemand', 'progressive').
* **extra_class** : [wpspw_recent_post_slider extra_class=""] (Enter extra CSS class for design customization ).

✅ **Checkout demo for better understanding**

[FREE DEMO](https://demo.essentialplugin.com/blog-designer-post-and-widget/?utm_source=WP&utm_medium=Blog-Designer&utm_campaign=Read-Me) | [PRO DEMO](https://demo.essentialplugin.com/prodemo/blog-designer-post-and-widget/?utm_source=WP&utm_medium=Blog-Designer&utm_campaign=Read-Me)

✅ **Essential Plugin Bundle Deal**

[Annual or Lifetime Bundle Deal](https://www.essentialplugin.com/pricing/?utm_source=WP&utm_medium=Blog-Designer&utm_campaign=Read-Me)

= ✅ Features include: =
* Added Gutenberg block support.
* Blog Post Grid
* Blog Post Slider
* Blog Post Widget
* Easy to add.
* Also work with Gutenberg shortcode block. 
* Elementor, Beaver and SiteOrigin Page Builder Native Support (New).
* Divi Page Builder Native Support (New).
* Fusion Page Builder (Avada) Native Support (New).
* Touch-enabled Navigation.
* Given 2 designs.
* Responsive.
* Responsive touch slider.
* Mouse Draggable.
* Use for header image slider.
* You can create multiple post slider with different options at single page or post.
* Fully responsive. Scales with its container.
* 100% Multi Language.

= How to install : =
[youtube https://www.youtube.com/watch?v=ecKRR0p_qYY]

= Privacy & Policy =
* We have also opt-in e-mail selection , once you download the plugin , so that we can inform you and nurture you about products and its features.

== Installation ==
1. Upload the 'blog-designer-for-post-and-widget' folder to the '/wp-content/plugins/' directory.
2. Activate the 'blog-designer-for-post-and-widget' list plugin through the 'Plugins' menu in WordPress.

= How to install : =
[youtube https://www.youtube.com/watch?v=ecKRR0p_qYY]

== Screenshots ==
1. Blog slider
2. Blog Grid
3. Also work with Gutenberg shortcode block.

== Changelog ==

= 2.7.5 (16, May 2025) =
* [*] Updated some URL's
* [*] Removed some unnecessary code.

= 2.7.4 (14, May 2025) =
* [*] Check compatibility with WordPress version 6.8.1

= 2.7.3 (20, January 2025) =
* [*] Check compatibility with WordPress version 6.7.1

= 2.7.2 (08, Nov 2024) =
* [*] Check compatibility with WordPress version 6.6.2

= 2.7.1 (25, July 2024) =
* [*] Check compatibility with WordPress version 6.6.1

= 2.7 (26, Nov 2023) =
* [*] Updated analytics SDK.
* [*] Check compatibility with WordPress version 6.4.1

= 2.6 (21, Aug 2023) =
* [*] Tested up to: 6.3

= 2.5.2 (7, Aug 2023) =
* [*] Fixed all security related issues.

= 2.5.1 (29, May 2023) =
* [*] Fix - Fixed slider initialization issue with Elementor vertical tab.
* [*] Tested up to: 6.2.2

= 2.5 (10, April 2023) =
* [*] Tested up to: 6.2
* [*] Update - Improve escaping functions for better security.
* [*] Update - Update optin screen.

= 2.4.1 (05, Jan 2023) =
* [*] Update - Update escaping functions for better security.

= 2.4 (04, Jan 2023) =
* [*] Update - Use escaping functions for better security.
* [*] Update - Update Slick slider JS to stable version 1.8.0
* [*] Update - JavaScript syntax for jQuery 3.0 and higher with compatibility to WordPress version 5.6.
* [*] Fix - Image is not visible in old browser when plugin lazy load is enabled.
* [*] Remove - Removed unnecessary files and images.
* [*] Remove - Removed .mo and .po files from 'languages' folder.
* [*] Tweak - Code optimization and performance improvements.

= 2.3 (09, Dec 2022) =
* [*] Tested up to: 6.1.1

= 2.2.9 (07, Nov 2022) =
* [*] Tested up to: 6.1

= 2.2.8 (24, May 2022) =
* [*] Tested up to: 6.0

= 2.2.7 (30, March 2022) =
* [*] Tested up to: 5.9.2
* [+] Added free vs pro functionality.

= 2.2.6 (16, March 2022) =
* [+] Added demo link
* [-] Removed some unwanted code and files.

= 2.2.5 (11, Feb 2022) =
* [-] Removed some unwanted code and files.

= 2.2.4 (03, Feb 2022) =
* [*] Tested up to: 5.9 
* [*] Solved Gutenberg wp-editor widget issue.

= ******* (15, Dec 2021) =
* [*] Minor fix.

= 2.2.3 (12, Nov 2021) =
* [*] Fix - Resolve Gutenberg WP-Editor script related issue.
* [*] Update - Add some text and links in Readme file.
* [*] Minor change in CSS.

= 2.2.2 (15, Sep 2021) =
* [*] Tested up to: 5.8.1
* [*] Updated demo link

= 2.2.1 (19, Aug 2021) =
* [*] Updated language file and JSON file.

= 2.2 (17, Aug 2021) =
* [*] Updated all external links
* [*] Tweak - Code optimization and performance improvements.
* [*] Fixed blocks initializer issue.

= 2.1.3 (31, May 2021) =
* [*] Tested up to: 5.7.2
* [*] Added - https link in our analytics code to avoid browser security warning.

= 2.1.2 (22, April 2021) =
* [+] New - Added fusion (Avada) page builder native support.
* [*] Tweak - Code optimization and performance improvements.

= 2.1.1 (09, Dec 2020) =
* [+] New - Added native shortcode support for Elementor, SiteOrigin and Beaver builder .
* [+] New - Added Divi page builder native support.
* [*] Tested up to: 5.6

= 2.1 (27, Oct 2020) =
* [*] Fixed - Removed image height from [wpspw_post] shortcode when using grid="1".
* [+] New - Click to copy the shortcode from the getting started page.
* [*] Update - Regular plugin maintenance. Updated readme file.
* [*] Added - Added our other Popular Plugins under Blog Designer --> Install Popular Plugins From Us. This will help you to save your time during creating a website.
* [*] jQuery( document ).ready(function($) is replaced with function( $ ) to solve the issue with 3rd party plugin and theme JS error.

= 2.0 (14-07-2020) =
* [*] Follow WordPress Detailed Plugin Guidelines for Offload Media and Analytics Code.

= 1.9 (14, May 2020) =
* [+] New - Added Gutenberg block support. Now use plugin easily with Gutenberg!
* [+] New - Added 'lazyload' shortcode parameter for all slider shortcodes. Now you can able to set lazy loading in two different method lazyload="ondemand" OR lazyload="progressive".
* [+] New - Added 'align' and 'extra_class' parameter for slider shortcode. Now both slider shortcode are support twenty-nineteen and twenty-twenty theme Gutenberg block align and additional class feature.
* [+] New - Add new classes sanitize function in function file.
* [+] Update - Minor change in CSS and JS.
* [*] Tweak - Pagination will if page is divided into multiple pages with "nextpage" tag.
* [*] Tweak - Pagination will work on single post and front page also.
* [*] Tweak - Code optimization and performance improvements.
* [*] Template File - Main design file has been updated. If you have override template file then verify with latest copy.

= 1.8 (27, Dec 2019) =
* [*] Updated features list.
* [*] Updated slider arrows.

= 1.7 (19, June 2019) =
* [+] New: Added order, orderby and sticky_posts parameter in both shortcode.
* [+] New: Added order, orderby and sticky_posts option in widget.

= 1.6.2 (07, Feb 2019) =
* [*] Minor change in Opt-in flow.

= 1.6.1 (18, Dec 2018) =
* [*] Update Opt-in flow.

= 1.6 (06, Dec 2018) =
* [*] Tested with WordPress 5.0 and Gutenberg.
* [*] Fixed some CSS issues.

= 1.5 (25, Aug 2018) =
* [*] Update - Updated plugin translation code. Now user can put plugin languages file in WordPress 'language' folder so plugin language file will not be loss while plugin update.
* [*] Fix - Some typo mistake in 'Getting Started' page.
* [*] Fix - Some warnings with widgets while using with WordPress customizer.
* [*] Fix – Added some missing translated string.
* [*] Tweak - Used 'wp_reset_postdata' instead of 'wp_reset_query'.
* [*] Tweak - Taken better care of Post Title as image ALT tag.
* [*] Removed - 'Title' attribute from link and image in widget.

= 1.4 (07, June 2018) =
* [*] Follow some WordPress Detailed Plugin Guidelines.

= 1.3 (07/05/2018) =
* [*] Fixed some design related issues, reported by users.

= 1.2 (10/3/2018) =
* [*] Created plugin separate menu 'Blog Designer' as per user's feedback.

= 1.1.3 (30/10/2017) =
* Fixed slider responsive issue for iPad and tablet.

= 1.1.2 (17/01/2017) =
* Fixed some CSS issue
* Made some changes in How It Work section

= 1.1.1(24/10/2016) =
* Added Pagination with 2 options
* Added how it work under "Post" tab

= 1.1 =
* Added 1 More design with design parameter

= 1.0 =
* Initial release.